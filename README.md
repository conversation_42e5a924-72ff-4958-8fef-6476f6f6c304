# Grievance Management Chatbot

A RAG-based chatbot system for handling complaint registration and status tracking, built with Streamlit, FastAPI, Groq LLM, and SQLite.

## Features

- **Conversational Interface**: Natural language interaction for complaint registration
- **RAG System**: Context-aware responses using vector embeddings
- **Complaint Management**: Register complaints and track status
- **API Integration**: RESTful APIs for complaint operations
- **Real-time Chat**: Streamlit-based chat interface
- **Database Storage**: SQLite database for persistent storage
- **LLM Integration**: Groq API for natural language processing

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│   FastAPI       │────│   SQLite DB     │
│   (Frontend)    │    │   (Backend)     │    │   (Storage)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│   Groq LLM      │──────────────┘
                        │   (AI Engine)   │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   RAG System    │
                        │   (Embeddings)  │
                        └─────────────────┘
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cyfuture-assign1
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your Groq API key
   ```

4. **Initialize the database**
   ```bash
   python init_db.py
   ```

## Configuration

### Groq API Key

1. Visit [Groq Console](https://console.groq.com/keys)
2. Create an account and generate an API key
3. Add the key to your `.env` file:
   ```
   GROQ_API_KEY=your_actual_api_key_here
   ```

### Database

The system uses SQLite by default. The database file `grievance_system.db` will be created automatically.

## Usage

### Starting the Application

1. **Start the API server** (Terminal 1):
   ```bash
   python api_server.py
   ```

2. **Start the Streamlit app** (Terminal 2):
   ```bash
   streamlit run app.py
   ```

3. **Open your browser** and navigate to `http://localhost:8501`

### Using the Chatbot

#### Registering a Complaint

1. Start a conversation: "I have a complaint about my laptop"
2. Provide your name when asked
3. Provide your mobile number
4. Describe your complaint in detail
5. Receive your Complaint ID

#### Checking Status

1. Say: "What's the status of my complaint?"
2. Provide either:
   - Your Complaint ID (e.g., CMP12345678)
   - Your registered mobile number

## API Endpoints

### Core Endpoints

- `POST /register-complaint` - Register a new complaint
- `GET /complaint-status/{complaint_id}` - Get complaint status
- `GET /user-complaints/{mobile}` - Get all complaints for a mobile number
- `PUT /update-complaint-status/{complaint_id}` - Update complaint status

### Additional Endpoints

- `GET /similar-complaints` - Find similar complaints using RAG
- `GET /contextual-response` - Get contextual response for complaint
- `GET /complaint-history/{complaint_id}` - Get status change history
- `POST /simulate-status-update/{complaint_id}` - Simulate status update

## Project Structure

```
cyfuture-assign1/
├── app.py                 # Main Streamlit application
├── api_server.py          # FastAPI backend server
├── database.py            # Database operations
├── llm_handler.py         # Groq LLM integration
├── rag_system.py          # RAG implementation
├── models.py              # Data models and schemas
├── config.py              # Configuration settings
├── init_db.py             # Database initialization
├── requirements.txt       # Python dependencies
├── .env.example           # Environment variables template
└── README.md              # This file
```

## Technical Details

### RAG Implementation

- **Embeddings**: Uses `sentence-transformers` for vector embeddings
- **Similarity Search**: Cosine similarity for finding relevant context
- **Knowledge Base**: Pre-defined scenarios and responses
- **Context Awareness**: Provides relevant information based on complaint details

### LLM Integration

- **Model**: Groq Mixtral-8x7b-32768
- **Intent Recognition**: Extracts user intent from messages
- **Response Generation**: Context-aware response generation
- **Categorization**: Automatic complaint categorization

### Database Schema

#### Complaints Table
- `complaint_id` (TEXT, PRIMARY KEY)
- `name` (TEXT)
- `mobile` (TEXT)
- `complaint_details` (TEXT)
- `category` (TEXT)
- `status` (TEXT)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### Status History Table
- `complaint_id` (TEXT, FOREIGN KEY)
- `old_status` (TEXT)
- `new_status` (TEXT)
- `notes` (TEXT)
- `changed_at` (TIMESTAMP)

## Testing

### Manual Testing

1. Test complaint registration flow
2. Test status checking with Complaint ID
3. Test status checking with mobile number
4. Test error handling for invalid inputs

### API Testing

Use tools like curl or Postman to test API endpoints:

```bash
# Register complaint
curl -X POST "http://localhost:8000/register-complaint" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "mobile": "+**********",
    "complaint_details": "Test complaint details",
    "category": "Hardware"
  }'

# Check status
curl "http://localhost:8000/complaint-status/CMP12345678"
```

## Troubleshooting

### Common Issues

1. **API Server not starting**
   - Check if port 8000 is available
   - Verify all dependencies are installed

2. **Groq API errors**
   - Verify your API key is correct
   - Check your Groq account quota

3. **Database errors**
   - Run `python init_db.py` to reinitialize
   - Check file permissions

4. **Streamlit not loading**
   - Ensure API server is running first
   - Check browser console for errors

### Logs

- API server logs appear in the terminal where you run `python api_server.py`
- Streamlit logs appear in the terminal where you run `streamlit run app.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is created for educational purposes as part of an assessment task.

## Support

For issues and questions, please check the troubleshooting section or create an issue in the repository.
