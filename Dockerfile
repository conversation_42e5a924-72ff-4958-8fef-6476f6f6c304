# Multi-stage Docker build for Grievance Management System
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/data /app/logs

# Initialize database
RUN python init_db.py

# Expose ports
EXPOSE 8000 8501

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Start API server in background\n\
echo "Starting API server..."\n\
python api_server.py &\n\
API_PID=$!\n\
\n\
# Wait for API server to be ready\n\
echo "Waiting for API server to be ready..."\n\
for i in {1..30}; do\n\
    if curl -f http://localhost:8000/ >/dev/null 2>&1; then\n\
        echo "API server is ready!"\n\
        break\n\
    fi\n\
    echo "Waiting... ($i/30)"\n\
    sleep 2\n\
done\n\
\n\
# Start Streamlit app\n\
echo "Starting Streamlit app..."\n\
streamlit run app.py --server.port=8501 --server.address=0.0.0.0 --server.headless=true &\n\
STREAMLIT_PID=$!\n\
\n\
# Function to handle shutdown\n\
shutdown() {\n\
    echo "Shutting down..."\n\
    kill $API_PID $STREAMLIT_PID 2>/dev/null || true\n\
    wait $API_PID $STREAMLIT_PID 2>/dev/null || true\n\
    exit 0\n\
}\n\
\n\
# Set up signal handlers\n\
trap shutdown SIGTERM SIGINT\n\
\n\
# Wait for processes\n\
wait $API_PID $STREAMLIT_PID\n\
' > /app/start.sh && chmod +x /app/start.sh

# Default command
CMD ["/app/start.sh"]
