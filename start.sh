#!/bin/bash
# Grievance Management System Startup Script

echo "🎯 Starting Grievance Management System..."
echo "=================================="

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
fi

# Start API server in background
echo "🔧 Starting API server..."
python src/api/api_server.py &
API_PID=$!

# Wait a moment for API to start
sleep 3

# Start frontend
echo "🎨 Starting frontend..."
streamlit run src/frontend/app.py --server.port 8501

# Cleanup on exit
trap "kill $API_PID" EXIT
