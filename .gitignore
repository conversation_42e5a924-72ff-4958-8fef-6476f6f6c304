# Grievance Management System - Git Ignore File
# This file prevents sensitive and unnecessary files from being uploaded to GitHub

# ===== SENSITIVE FILES =====
# API Keys and Environment Variables
.env
.env.local
.env.development
.env.test
.env.production
*.key
*_key
*_secret
config.ini
secrets.json

# ===== PYTHON =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
.venv/

# ===== DATABASE FILES =====
# SQLite databases (keep structure, remove data)
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite

# ===== LOGS =====
# Log files
*.log
logs/
*.log.*

# ===== IDE =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml

# Jupyter Notebook
.ipynb_checkpoints

# ===== OS =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# ===== DOCKER =====
# Docker volumes and data
docker-data/
.docker/

# ===== TEMPORARY FILES =====
# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo

# ===== VECTOR DATABASES =====
# Vector database files (can be regenerated)
vector_db/
*.faiss
*.index

# ===== LARGE FILES =====
# Large model files (should be downloaded separately)
*.bin
*.model
models/
embeddings/

# ===== DOCUMENTATION BUILDS =====
# Sphinx documentation
docs/_build/

# ===== NODE MODULES (if any) =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== BACKUP FILES =====
*.bak
*.backup
*.old

# ===== SPECIFIC TO THIS PROJECT =====
# Not required files for GitHub
Not_Required/

# Test databases
test_*.db
*_test.db
