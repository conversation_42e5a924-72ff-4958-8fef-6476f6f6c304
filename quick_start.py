#!/usr/bin/env python3
"""
Quick Start Script for Grievance Management System
One-command setup and launch
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header():
    """Print quick start header"""
    print("🚀 GRIEVANCE MANAGEMENT SYSTEM - QUICK START")
    print("=" * 60)
    print("⚡ One-command setup and launch")
    print("🎯 Professional AI-powered complaint management")
    print("=" * 60)
    print()

def check_requirements():
    """Quick requirements check"""
    print("🔍 Quick system check...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print("✅ Python version compatible")
    return True

def install_core_deps():
    """Install only essential dependencies"""
    print("📦 Installing core dependencies...")
    
    core_deps = [
        "streamlit",
        "fastapi", 
        "uvicorn",
        "pandas",
        "plotly",
        "requests"
    ]
    
    try:
        for dep in core_deps:
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, check=True)
        
        print("✅ Core dependencies installed")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ Dependency installation failed")
        return False

def setup_minimal():
    """Minimal setup for quick start"""
    print("⚙️ Minimal setup...")
    
    # Create essential directories
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # Create basic .env if not exists
    if not Path(".env").exists():
        env_content = """API_HOST=0.0.0.0
API_PORT=8000
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
"""
        Path(".env").write_text(env_content)
    
    print("✅ Minimal setup completed")
    return True

def start_services():
    """Start API and frontend services"""
    print("🚀 Starting services...")
    
    try:
        # Start API server
        print("   Starting API server...")
        api_process = subprocess.Popen([
            sys.executable, "src/api/api_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for API to start
        time.sleep(3)
        
        # Start frontend
        print("   Starting frontend...")
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", 
            "src/frontend/app.py", "--server.port", "8501"
        ])
        
        print("✅ Services started successfully!")
        print()
        print("🌐 Access your application:")
        print("   • Main App: http://localhost:8501")
        print("   • Admin Panel: Click 'Admin Dashboard' → admin/admin123")
        print("   • API Docs: http://localhost:8000/docs")
        print()
        print("Press Ctrl+C to stop all services")
        
        # Wait for user interrupt
        try:
            frontend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping services...")
            api_process.terminate()
            frontend_process.terminate()
            print("✅ Services stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start services: {e}")
        return False

def main():
    """Quick start main function"""
    print_header()
    
    # Quick setup steps
    steps = [
        ("Requirements", check_requirements),
        ("Dependencies", install_core_deps),
        ("Setup", setup_minimal),
        ("Services", start_services)
    ]
    
    for step_name, step_func in steps:
        if not step_func():
            print(f"❌ {step_name} failed. Please run full setup:")
            print("   python scripts/enhanced_setup.py")
            sys.exit(1)

if __name__ == "__main__":
    main()
