from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class ComplaintStatus(str, Enum):
    REGISTERED = "Registered"
    IN_PROGRESS = "In Progress"
    UNDER_REVIEW = "Under Review"
    RESOLVED = "Resolved"
    CLOSED = "Closed"
    REJECTED = "Rejected"

class ComplaintCategory(str, Enum):
    HARDWARE = "Hardware"
    SOFTWARE = "Software"
    NETWORK = "Network"
    ACCOUNT = "Account"
    BILLING = "Billing"
    SERVICE = "Service"
    OTHER = "Other"

class ComplaintRequest(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    mobile: str = Field(..., pattern=r"^\+?[1-9]\d{1,14}$")
    complaint_details: str = Field(..., min_length=10, max_length=1000)
    category: Optional[ComplaintCategory] = ComplaintCategory.OTHER

class ComplaintResponse(BaseModel):
    complaint_id: str
    name: str
    mobile: str
    complaint_details: str
    category: str
    status: ComplaintStatus
    created_at: datetime
    updated_at: datetime

class Complaint(BaseModel):
    complaint_id: str
    name: str
    mobile: str
    complaint_details: str
    category: ComplaintCategory
    status: ComplaintStatus
    created_at: datetime
    updated_at: datetime

class StatusUpdateRequest(BaseModel):
    complaint_id: str
    status: ComplaintStatus
    notes: Optional[str] = None

class ChatMessage(BaseModel):
    role: str  # "user" or "assistant"
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)

class ChatSession(BaseModel):
    session_id: str
    messages: List[ChatMessage] = []
    user_context: dict = {}
    created_at: datetime = Field(default_factory=datetime.now)

class UserContext(BaseModel):
    name: Optional[str] = None
    mobile: Optional[str] = None
    current_step: str = "initial"  # initial, collecting_name, collecting_mobile, collecting_details
    complaint_details: Optional[str] = None
    pending_complaint: Optional[dict] = None
