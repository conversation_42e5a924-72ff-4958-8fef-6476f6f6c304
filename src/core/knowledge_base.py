import os
import json
import PyPDF2
import docx
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import sqlite3
from pathlib import Path
import re

class KnowledgeBase:
    """Enhanced Knowledge Base with PDF/Document support and RAG capabilities"""
    
    def __init__(self, knowledge_dir: str = "knowledge_base"):
        self.knowledge_dir = Path(knowledge_dir)
        self.knowledge_dir.mkdir(exist_ok=True)
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Initialize knowledge base
        self.documents = []
        self.embeddings = None
        
        # Load knowledge base
        self._load_default_knowledge()
        self._load_documents_from_directory()
        self._create_embeddings()
    
    def _load_default_knowledge(self):
        """Load default customer service knowledge base"""
        default_knowledge = [
            {
                "id": "kb_001",
                "title": "Delayed Delivery Policy",
                "content": """
                Delayed Delivery Handling Procedure:
                1. Acknowledge the customer's concern immediately
                2. Collect order details (order number, expected delivery date)
                3. Check tracking information in the system
                4. Provide realistic timeline for resolution
                5. Offer compensation if delay exceeds 3 business days
                6. Follow up within 24 hours with updates
                
                Standard Response: "I sincerely apologize for the delay in your delivery. Let me check your order status and provide you with an update."
                """,
                "category": "Delivery",
                "keywords": ["delayed delivery", "late order", "shipping delay", "order not arrived"],
                "resolution_time": "24-48 hours",
                "escalation_required": False
            },
            {
                "id": "kb_002", 
                "title": "Product Quality Issues",
                "content": """
                Product Quality Complaint Handling:
                1. Express empathy and understanding
                2. Collect detailed information about the issue
                3. Request photos if applicable
                4. Determine if replacement or refund is appropriate
                5. Initiate return process if needed
                6. Document issue for quality improvement
                
                Standard Response: "I understand your disappointment with the product quality. We take these concerns seriously and will resolve this promptly."
                """,
                "category": "Quality",
                "keywords": ["defective product", "poor quality", "damaged item", "not as described"],
                "resolution_time": "2-3 business days",
                "escalation_required": False
            },
            {
                "id": "kb_003",
                "title": "Billing and Payment Issues",
                "content": """
                Billing Dispute Resolution Process:
                1. Listen carefully to the customer's concern
                2. Review billing history and transaction details
                3. Identify any discrepancies or errors
                4. Explain charges clearly if they are correct
                5. Process refunds or adjustments if errors are found
                6. Provide detailed explanation of resolution
                
                Standard Response: "I'll review your billing details immediately and ensure any discrepancies are resolved quickly."
                """,
                "category": "Billing",
                "keywords": ["wrong charge", "billing error", "payment issue", "refund request"],
                "resolution_time": "3-5 business days",
                "escalation_required": True
            },
            {
                "id": "kb_004",
                "title": "Customer Service Experience",
                "content": """
                Poor Service Experience Handling:
                1. Apologize sincerely for the poor experience
                2. Listen actively to understand specific issues
                3. Document all concerns raised
                4. Provide immediate solutions where possible
                5. Escalate to supervisor if needed
                6. Follow up to ensure satisfaction
                
                Standard Response: "I sincerely apologize for the poor service experience. Your feedback is valuable and I'll ensure this is addressed immediately."
                """,
                "category": "Service",
                "keywords": ["poor service", "rude staff", "bad experience", "unprofessional"],
                "resolution_time": "1-2 business days",
                "escalation_required": True
            },
            {
                "id": "kb_005",
                "title": "Technical Support Issues",
                "content": """
                Technical Issue Resolution Process:
                1. Gather detailed information about the technical problem
                2. Attempt basic troubleshooting steps
                3. Escalate to technical team if needed
                4. Provide workarounds if available
                5. Keep customer updated on progress
                6. Verify resolution with customer
                
                Standard Response: "I'll help you resolve this technical issue. Let me gather some information and connect you with our technical support team."
                """,
                "category": "Technical",
                "keywords": ["technical issue", "software problem", "app not working", "system error"],
                "resolution_time": "4-6 hours",
                "escalation_required": True
            }
        ]
        
        self.documents.extend(default_knowledge)
    
    def _load_documents_from_directory(self):
        """Load documents from knowledge base directory"""
        if not self.knowledge_dir.exists():
            return
        
        for file_path in self.knowledge_dir.glob("*"):
            if file_path.is_file():
                try:
                    if file_path.suffix.lower() == '.pdf':
                        content = self._extract_pdf_content(file_path)
                    elif file_path.suffix.lower() == '.docx':
                        content = self._extract_docx_content(file_path)
                    elif file_path.suffix.lower() == '.txt':
                        content = self._extract_txt_content(file_path)
                    elif file_path.suffix.lower() == '.json':
                        content = self._extract_json_content(file_path)
                    else:
                        continue
                    
                    if content:
                        doc = {
                            "id": f"doc_{file_path.stem}",
                            "title": file_path.stem.replace('_', ' ').title(),
                            "content": content,
                            "source": str(file_path),
                            "category": "Document",
                            "keywords": self._extract_keywords(content)
                        }
                        self.documents.append(doc)
                        
                except Exception as e:
                    print(f"Error loading document {file_path}: {e}")
    
    def _extract_pdf_content(self, file_path: Path) -> str:
        """Extract text content from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                content = ""
                for page in pdf_reader.pages:
                    content += page.extract_text() + "\n"
                return content.strip()
        except Exception as e:
            print(f"Error extracting PDF content: {e}")
            return ""
    
    def _extract_docx_content(self, file_path: Path) -> str:
        """Extract text content from DOCX file"""
        try:
            doc = docx.Document(file_path)
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            return content.strip()
        except Exception as e:
            print(f"Error extracting DOCX content: {e}")
            return ""
    
    def _extract_txt_content(self, file_path: Path) -> str:
        """Extract text content from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except Exception as e:
            print(f"Error extracting TXT content: {e}")
            return ""
    
    def _extract_json_content(self, file_path: Path) -> str:
        """Extract content from JSON knowledge base file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                if isinstance(data, list):
                    content = ""
                    for item in data:
                        if isinstance(item, dict):
                            content += json.dumps(item, indent=2) + "\n"
                    return content
                elif isinstance(data, dict):
                    return json.dumps(data, indent=2)
                else:
                    return str(data)
        except Exception as e:
            print(f"Error extracting JSON content: {e}")
            return ""
    
    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from content"""
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        # Remove common stop words
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
        keywords = [word for word in set(words) if word not in stop_words]
        return keywords[:20]  # Return top 20 keywords
    
    def _create_embeddings(self):
        """Create embeddings for all documents"""
        if not self.documents:
            return
        
        # Combine title and content for better embeddings
        texts = []
        for doc in self.documents:
            text = f"{doc['title']} {doc['content']}"
            texts.append(text)
        
        self.embeddings = self.embedding_model.encode(texts)
    
    def search_knowledge(self, query: str, top_k: int = 3, min_similarity: float = 0.3) -> List[Dict[str, Any]]:
        """Search knowledge base using semantic similarity"""
        if not self.documents or self.embeddings is None:
            return []
        
        # Create query embedding
        query_embedding = self.embedding_model.encode([query])
        
        # Calculate similarities
        similarities = cosine_similarity(query_embedding, self.embeddings)[0]
        
        # Get top-k results above minimum similarity
        results = []
        for i, similarity in enumerate(similarities):
            if similarity >= min_similarity:
                doc = self.documents[i].copy()
                doc['similarity_score'] = float(similarity)
                results.append(doc)
        
        # Sort by similarity score
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return results[:top_k]
    
    def get_contextual_response(self, complaint_text: str) -> Dict[str, Any]:
        """Get contextual response based on knowledge base"""
        relevant_docs = self.search_knowledge(complaint_text)
        
        if not relevant_docs:
            return {
                "response": "Thank you for your complaint. We'll review it and get back to you soon.",
                "category": "General",
                "estimated_resolution": "3-5 business days",
                "follow_up_questions": ["Could you provide more specific details about the issue?"],
                "knowledge_source": None
            }
        
        best_match = relevant_docs[0]
        
        # Extract response from content if available
        content = best_match['content']
        response_match = re.search(r'Standard Response:\s*"([^"]+)"', content)
        if response_match:
            response = response_match.group(1)
        else:
            response = f"I understand your concern regarding {best_match['category'].lower()}. Let me help you resolve this issue."
        
        # Extract resolution time
        resolution_match = re.search(r'resolution_time["\']:\s*["\']([^"\']+)["\']', content)
        if resolution_match:
            resolution_time = resolution_match.group(1)
        else:
            resolution_time = "2-3 business days"
        
        return {
            "response": response,
            "category": best_match.get('category', 'General'),
            "estimated_resolution": resolution_time,
            "follow_up_questions": self._generate_follow_up_questions(best_match['category']),
            "knowledge_source": best_match['title'],
            "similarity_score": best_match['similarity_score']
        }
    
    def _generate_follow_up_questions(self, category: str) -> List[str]:
        """Generate follow-up questions based on category"""
        question_map = {
            "Delivery": [
                "What is your order number?",
                "What was the expected delivery date?",
                "Have you received any tracking information?"
            ],
            "Quality": [
                "Can you describe the specific quality issue?",
                "Do you have photos of the problem?",
                "When did you first notice this issue?"
            ],
            "Billing": [
                "Which specific charge are you questioning?",
                "Do you have the transaction or invoice number?",
                "When did this charge appear?"
            ],
            "Service": [
                "Can you describe the specific service issue?",
                "When did this interaction occur?",
                "Who did you speak with previously?"
            ],
            "Technical": [
                "What specific error are you experiencing?",
                "What device or platform are you using?",
                "When did this issue first occur?"
            ]
        }
        
        return question_map.get(category, [
            "Could you provide more details about the issue?",
            "When did this problem first occur?",
            "Have you tried any solutions already?"
        ])
    
    def add_document(self, title: str, content: str, category: str = "Custom") -> bool:
        """Add a new document to the knowledge base"""
        try:
            doc = {
                "id": f"custom_{len(self.documents)}",
                "title": title,
                "content": content,
                "category": category,
                "keywords": self._extract_keywords(content),
                "source": "manual_entry"
            }
            
            self.documents.append(doc)
            self._create_embeddings()  # Recreate embeddings
            return True
            
        except Exception as e:
            print(f"Error adding document: {e}")
            return False
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """Get all documents in the knowledge base"""
        return [
            {
                "id": doc["id"],
                "title": doc["title"],
                "category": doc.get("category", "Unknown"),
                "source": doc.get("source", "default"),
                "keywords": doc.get("keywords", [])[:5]  # First 5 keywords
            }
            for doc in self.documents
        ]
