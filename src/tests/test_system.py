#!/usr/bin/env python3
"""
Test script for Grievance Management System
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://127.0.0.1:8000"

def test_api_connection():
    """Test if API server is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        return False

def test_complaint_registration():
    """Test complaint registration"""
    print("\n🧪 Testing complaint registration...")
    
    complaint_data = {
        "name": "Test User",
        "mobile": "+1234567890",
        "complaint_details": "This is a test complaint about laptop issues. The screen is flickering and keyboard is not responding properly.",
        "category": "Hardware"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            complaint_id = result["complaint_id"]
            print(f"✅ Complaint registered successfully: {complaint_id}")
            return complaint_id
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error during registration: {e}")
        return None

def test_status_check(complaint_id):
    """Test complaint status check"""
    print(f"\n🧪 Testing status check for {complaint_id}...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/complaint-status/{complaint_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status retrieved: {result['status']}")
            print(f"   Category: {result['category']}")
            print(f"   Created: {result['created_at']}")
            return True
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during status check: {e}")
        return False

def test_user_complaints():
    """Test getting user complaints by mobile"""
    print(f"\n🧪 Testing user complaints lookup...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/user-complaints/+1234567890",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Found {len(result)} complaints for user")
            for complaint in result:
                print(f"   - {complaint['complaint_id']}: {complaint['status']}")
            return True
        else:
            print(f"❌ User complaints lookup failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during user complaints lookup: {e}")
        return False

def test_contextual_response():
    """Test RAG contextual response"""
    print(f"\n🧪 Testing RAG contextual response...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/contextual-response",
            params={"complaint_details": "My laptop screen is broken and won't display anything"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Contextual response generated")
            print(f"   Category: {result.get('category', 'N/A')}")
            print(f"   Response: {result.get('response', 'N/A')[:100]}...")
            return True
        else:
            print(f"❌ Contextual response failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during contextual response: {e}")
        return False

def test_database_operations():
    """Test database operations"""
    print(f"\n🧪 Testing database operations...")
    
    try:
        from database import DatabaseManager
        from models import ComplaintRequest, ComplaintCategory
        
        db_manager = DatabaseManager()
        
        # Test complaint registration
        complaint = ComplaintRequest(
            name="DB Test User",
            mobile="+9876543210",
            complaint_details="Database test complaint for network connectivity issues",
            category=ComplaintCategory.NETWORK
        )
        
        result = db_manager.register_complaint(complaint)
        print(f"✅ Database complaint registered: {result.complaint_id}")
        
        # Test retrieval
        retrieved = db_manager.get_complaint_by_id(result.complaint_id)
        if retrieved:
            print(f"✅ Database complaint retrieved: {retrieved.name}")
        else:
            print(f"❌ Failed to retrieve complaint from database")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

def test_llm_integration():
    """Test LLM integration"""
    print(f"\n🧪 Testing LLM integration...")
    
    try:
        from llm_handler import LLMHandler
        from models import UserContext
        
        llm_handler = LLMHandler()
        context = UserContext()
        
        # Test intent extraction
        intent_data = llm_handler.extract_intent("I have a complaint about my laptop", context)
        print(f"✅ Intent extracted: {intent_data.get('intent', 'unknown')}")
        
        # Test response generation
        response = llm_handler.generate_response("Hello", context, intent_data)
        print(f"✅ Response generated: {response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM integration test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Grievance Management System - Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test API connection
    total_tests += 1
    if test_api_connection():
        tests_passed += 1
        
        # Only run API tests if server is running
        total_tests += 4
        
        # Test complaint registration
        complaint_id = test_complaint_registration()
        if complaint_id:
            tests_passed += 1
            
            # Test status check
            if test_status_check(complaint_id):
                tests_passed += 1
        
        # Test user complaints
        if test_user_complaints():
            tests_passed += 1
            
        # Test contextual response
        if test_contextual_response():
            tests_passed += 1
    
    # Test database operations
    total_tests += 1
    if test_database_operations():
        tests_passed += 1
    
    # Test LLM integration (only if API key is available)
    try:
        from config import Config
        if Config.GROQ_API_KEY and Config.GROQ_API_KEY != "":
            total_tests += 1
            if test_llm_integration():
                tests_passed += 1
        else:
            print("\n⚠️  Skipping LLM tests - No API key configured")
    except:
        print("\n⚠️  Skipping LLM tests - Configuration error")
    
    # Summary
    print(f"\n📊 Test Results")
    print("=" * 30)
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    print(f"Success Rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! System is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())
