#!/usr/bin/env python3
"""
Demo script to showcase the Grievance Management System
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://127.0.0.1:8000"

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n🔸 Step {step_num}: {description}")
    print("-" * 40)

def demo_complaint_registration():
    """Demo the complaint registration process"""
    print_header("DEMO: COMPLAINT REGISTRATION PROCESS")
    
    print_step(1, "Registering a new complaint")
    
    complaint_data = {
        "name": "Demo User",
        "mobile": "+1234567890",
        "complaint_details": "My laptop screen is flickering and the keyboard is not responding properly. This started happening yesterday after a software update.",
        "category": "Hardware"
    }
    
    print("📝 Complaint Details:")
    for key, value in complaint_data.items():
        print(f"   • {key.replace('_', ' ').title()}: {value}")
    
    try:
        print("\n⏳ Sending request to API...")
        response = requests.post(
            f"{API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            complaint_id = result["complaint_id"]
            
            print("✅ SUCCESS! Complaint registered successfully")
            print(f"📋 Complaint ID: {complaint_id}")
            print(f"📊 Status: {result['status']}")
            print(f"🏷️ Category: {result['category']}")
            print(f"📅 Created: {result['created_at'][:19]}")
            
            return complaint_id
        else:
            print(f"❌ ERROR: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return None

def demo_status_check(complaint_id):
    """Demo the status checking process"""
    print_header("DEMO: STATUS CHECK PROCESS")
    
    print_step(1, f"Checking status for complaint ID: {complaint_id}")
    
    try:
        print("⏳ Fetching complaint status...")
        response = requests.get(
            f"{API_BASE_URL}/complaint-status/{complaint_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS! Status retrieved")
            print(f"📋 Complaint ID: {result['complaint_id']}")
            print(f"👤 Name: {result['name']}")
            print(f"📱 Mobile: {result['mobile']}")
            print(f"📊 Status: {result['status']}")
            print(f"🏷️ Category: {result['category']}")
            print(f"📅 Created: {result['created_at'][:19]}")
            print(f"🔄 Updated: {result['updated_at'][:19]}")
            print(f"📝 Details: {result['complaint_details'][:100]}...")
            
            return True
        else:
            print(f"❌ ERROR: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False

def demo_user_complaints():
    """Demo fetching all complaints for a user"""
    print_header("DEMO: USER COMPLAINTS LOOKUP")
    
    mobile = "+1234567890"
    print_step(1, f"Fetching all complaints for mobile: {mobile}")
    
    try:
        print("⏳ Fetching user complaints...")
        response = requests.get(
            f"{API_BASE_URL}/user-complaints/{mobile}",
            timeout=10
        )
        
        if response.status_code == 200:
            complaints = response.json()
            
            print(f"✅ SUCCESS! Found {len(complaints)} complaint(s)")
            
            for i, complaint in enumerate(complaints, 1):
                print(f"\n📋 Complaint #{i}:")
                print(f"   • ID: {complaint['complaint_id']}")
                print(f"   • Status: {complaint['status']}")
                print(f"   • Category: {complaint['category']}")
                print(f"   • Created: {complaint['created_at'][:19]}")
            
            return True
        else:
            print(f"❌ ERROR: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False

def demo_rag_features():
    """Demo RAG system features"""
    print_header("DEMO: RAG SYSTEM FEATURES")
    
    print_step(1, "Testing contextual response generation")
    
    complaint_text = "My laptop screen is broken and won't display anything"
    
    try:
        print(f"📝 Input: {complaint_text}")
        print("⏳ Generating contextual response...")
        
        response = requests.get(
            f"{API_BASE_URL}/contextual-response",
            params={"complaint_details": complaint_text},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS! Contextual response generated")
            print(f"🏷️ Detected Category: {result.get('category', 'N/A')}")
            print(f"⏱️ Estimated Resolution: {result.get('estimated_resolution', 'N/A')}")
            print(f"💬 Response: {result.get('response', 'N/A')}")
            
            return True
        else:
            print(f"❌ ERROR: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False

def check_system_status():
    """Check if the system is ready for demo"""
    print_header("SYSTEM STATUS CHECK")
    
    try:
        print("⏳ Checking API server status...")
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        
        if response.status_code == 200:
            print("✅ API Server: Online")
            return True
        else:
            print(f"❌ API Server: Error {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Server: Offline ({e})")
        print("\n🚀 To start the API server, run:")
        print("   python api_server.py")
        return False

def main():
    """Main demo function"""
    print_header("GRIEVANCE MANAGEMENT SYSTEM - LIVE DEMO")
    print("🎯 This demo showcases the complete functionality")
    print("📅 Demo Date:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # Check system status
    if not check_system_status():
        print("\n❌ System not ready for demo. Please start the API server first.")
        return
    
    print("\n🎉 System is ready! Starting demo...")
    time.sleep(2)
    
    # Demo 1: Complaint Registration
    complaint_id = demo_complaint_registration()
    if not complaint_id:
        print("\n❌ Demo failed at registration step")
        return
    
    time.sleep(2)
    
    # Demo 2: Status Check
    if not demo_status_check(complaint_id):
        print("\n❌ Demo failed at status check step")
        return
    
    time.sleep(2)
    
    # Demo 3: User Complaints
    if not demo_user_complaints():
        print("\n❌ Demo failed at user complaints step")
        return
    
    time.sleep(2)
    
    # Demo 4: RAG Features
    if not demo_rag_features():
        print("\n❌ Demo failed at RAG features step")
        return
    
    # Demo Complete
    print_header("DEMO COMPLETED SUCCESSFULLY! 🎉")
    print("✅ All features demonstrated successfully")
    print("🎯 The Grievance Management System is fully operational")
    print("\n📱 Access the web interface at: http://localhost:8505")
    print("🔗 API documentation at: http://127.0.0.1:8000/docs")
    
    print("\n🚀 Next Steps:")
    print("   • Try the web interface for interactive chat")
    print("   • Test with different complaint types")
    print("   • Explore the API endpoints")
    print("   • Check the database for stored data")

if __name__ == "__main__":
    main()
