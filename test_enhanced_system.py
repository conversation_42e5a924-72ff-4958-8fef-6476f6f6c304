#!/usr/bin/env python3
"""
Enhanced Grievance Management System - Comprehensive Test
Tests all new features including email support, knowledge base, and RAG integration
"""

import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_email_support():
    """Test email field support in models and database"""
    print("🧪 Testing Email Support...")
    
    try:
        from models.models import ComplaintRequest, UserContext
        
        # Test ComplaintRequest with email
        complaint = ComplaintRequest(
            name="Test User",
            mobile="1234567890",
            email="<EMAIL>",
            complaint_details="Test complaint with email support",
            category="Other"
        )
        
        print(f"✅ ComplaintRequest with email: {complaint.email}")
        
        # Test UserContext with email
        context = UserContext(
            name="Test User",
            mobile="1234567890", 
            email="<EMAIL>",
            current_step="collecting_email"
        )
        
        print(f"✅ UserContext with email: {context.email}")
        return True
        
    except Exception as e:
        print(f"❌ Email support test failed: {e}")
        return False

def test_knowledge_base():
    """Test knowledge base functionality"""
    print("\n🧪 Testing Knowledge Base...")
    
    try:
        from core.knowledge_base import KnowledgeBase
        
        # Initialize knowledge base
        kb = KnowledgeBase()
        
        # Test search functionality
        results = kb.search_knowledge("delayed delivery", top_k=2)
        print(f"✅ Knowledge base search returned {len(results)} results")
        
        if results:
            print(f"✅ Top result: {results[0]['title']}")
            print(f"✅ Similarity score: {results[0]['similarity_score']:.3f}")
        
        # Test contextual response
        response = kb.get_contextual_response("My order is delayed")
        print(f"✅ Contextual response: {response['response'][:50]}...")
        print(f"✅ Category: {response['category']}")
        print(f"✅ Resolution time: {response['estimated_resolution']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Knowledge base test failed: {e}")
        return False

def test_enhanced_rag():
    """Test enhanced RAG system"""
    print("\n🧪 Testing Enhanced RAG System...")
    
    try:
        from core.rag_system import RAGSystem
        
        # Initialize RAG system
        rag = RAGSystem()
        
        # Test contextual response
        response = rag.get_contextual_response("My laptop is not working properly")
        print(f"✅ RAG contextual response: {response['response'][:50]}...")
        print(f"✅ Category: {response['category']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced RAG test failed: {e}")
        return False

def test_database_email():
    """Test database with email support"""
    print("\n🧪 Testing Database Email Support...")
    
    try:
        from database.database import DatabaseManager
        from models.models import ComplaintRequest, ComplaintCategory
        
        # Initialize database
        db = DatabaseManager()
        db.init_database()
        
        # Create test complaint with email
        complaint = ComplaintRequest(
            name="Email Test User",
            mobile="9999999999",
            email="<EMAIL>",
            complaint_details="Testing email field in database",
            category=ComplaintCategory.OTHER
        )
        
        # Register complaint
        result = db.register_complaint(complaint)
        print(f"✅ Complaint registered with email: {result.email}")
        
        # Retrieve complaint
        retrieved = db.get_complaint_by_id(result.complaint_id)
        if retrieved and retrieved.email == "<EMAIL>":
            print(f"✅ Email retrieved correctly: {retrieved.email}")
            return True
        else:
            print("❌ Email not retrieved correctly")
            return False
            
    except Exception as e:
        print(f"❌ Database email test failed: {e}")
        return False

def test_conversation_flow():
    """Test enhanced conversation flow"""
    print("\n🧪 Testing Enhanced Conversation Flow...")
    
    try:
        from frontend.app import validate_email, validate_mobile, validate_name
        
        # Test validation functions
        assert validate_email("<EMAIL>") == True
        assert validate_email("invalid-email") == False
        print("✅ Email validation working")
        
        assert validate_mobile("1234567890") == True
        assert validate_mobile("123") == False
        print("✅ Mobile validation working")
        
        assert validate_name("John Doe") == True
        assert validate_name("123") == False
        print("✅ Name validation working")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversation flow test failed: {e}")
        return False

def test_api_integration():
    """Test API with email support"""
    print("\n🧪 Testing API Integration...")
    
    try:
        import requests
        from config.config import Config
        
        # Test API health
        response = requests.get(f"{Config.API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ API is running")
            
            # Test complaint registration with email
            complaint_data = {
                "name": "API Test User",
                "mobile": "**********",
                "email": "<EMAIL>",
                "complaint_details": "Testing API with email support",
                "category": "Other"
            }
            
            reg_response = requests.post(
                f"{Config.API_BASE_URL}/register-complaint",
                json=complaint_data,
                timeout=10
            )
            
            if reg_response.status_code == 200:
                data = reg_response.json()
                print(f"✅ API complaint registered with email: {data.get('email')}")
                return True
            else:
                print(f"❌ API registration failed: {reg_response.status_code}")
                return False
        else:
            print("❌ API not accessible")
            return False
            
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False

def create_test_knowledge_documents():
    """Create test knowledge base documents"""
    print("\n📚 Creating Test Knowledge Documents...")
    
    try:
        os.makedirs("knowledge_base", exist_ok=True)
        
        # Create a test FAQ JSON
        test_faq = [
            {
                "id": "test_001",
                "question": "How to track my order?",
                "answer": "You can track your order using the tracking number provided in your confirmation email.",
                "category": "Delivery",
                "keywords": ["track", "order", "delivery"]
            }
        ]
        
        with open("knowledge_base/test_faq.json", "w") as f:
            json.dump(test_faq, f, indent=2)
        
        # Create a test policy document
        test_policy = """
        TEST CUSTOMER SERVICE POLICY
        
        Delivery Issues:
        - Acknowledge within 2 hours
        - Provide tracking information
        - Offer compensation for delays over 3 days
        
        Standard Response: "I apologize for the delivery delay. Let me check your order status immediately."
        """
        
        with open("knowledge_base/test_policy.txt", "w") as f:
            f.write(test_policy)
        
        print("✅ Test knowledge documents created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test documents: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🚀 Enhanced Grievance Management System - Comprehensive Test")
    print("=" * 60)
    
    tests = [
        ("Email Support", test_email_support),
        ("Knowledge Base", test_knowledge_base),
        ("Enhanced RAG", test_enhanced_rag),
        ("Database Email", test_database_email),
        ("Conversation Flow", test_conversation_flow),
        ("API Integration", test_api_integration),
    ]
    
    # Create test documents first
    create_test_knowledge_documents()
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Enhanced system is working correctly.")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
