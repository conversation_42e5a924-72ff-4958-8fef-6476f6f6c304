#!/usr/bin/env python3
"""
Setup script for Grievance Management System
"""

import os
import sys
import subprocess
import shutil

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up environment file"""
    if not os.path.exists('.env'):
        print("📝 Setting up environment file...")
        shutil.copy('.env.example', '.env')
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env and add your Groq API key")
        return False
    else:
        print("✅ Environment file already exists")
        return True

def make_scripts_executable():
    """Make Python scripts executable on Unix systems"""
    if os.name != 'nt':  # Not Windows
        scripts = ['init_db.py', 'start_app.py', 'test_system.py', 'setup.py']
        for script in scripts:
            if os.path.exists(script):
                os.chmod(script, 0o755)
        print("✅ Made scripts executable")

def display_next_steps():
    """Display next steps for the user"""
    print("\n🎯 Next Steps:")
    print("=" * 30)
    print("1. Get a Groq API key from: https://console.groq.com/keys")
    print("2. Edit the .env file and add your API key:")
    print("   GROQ_API_KEY=your_actual_api_key_here")
    print("3. Initialize the database:")
    print("   python init_db.py")
    print("4. Start the application:")
    print("   python start_app.py")
    print("   OR manually:")
    print("   - Terminal 1: python api_server.py")
    print("   - Terminal 2: streamlit run app.py")
    print("5. Test the system:")
    print("   python test_system.py")

def main():
    """Main setup function"""
    print("🚀 Grievance Management System Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Setup environment
    env_ready = setup_environment()
    
    # Make scripts executable
    make_scripts_executable()
    
    print("\n✅ Setup completed successfully!")
    
    if not env_ready:
        display_next_steps()
        return 1
    else:
        print("🎉 System is ready to use!")
        print("Run: python start_app.py")
    
    return 0

if __name__ == "__main__":
    exit(main())
