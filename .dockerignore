# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
*.log
logs/

# Database (if you want to exclude existing data)
# *.db

# Environment variables
.env
.env.local

# Documentation
*.md
docs/

# Test files
test_*
*_test.py
tests/

# Temporary files
tmp/
temp/
*.tmp

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/
