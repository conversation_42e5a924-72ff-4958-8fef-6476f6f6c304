#!/usr/bin/env python3
"""
Generate Architecture Diagram for Grievance Management System
Creates a comprehensive system architecture visualization
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_architecture_diagram():
    """Create comprehensive architecture diagram"""
    
    # Create figure and axis
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # Color scheme
    colors = {
        'frontend': '#3498db',
        'backend': '#e74c3c',
        'database': '#f39c12',
        'ai': '#9b59b6',
        'infrastructure': '#2ecc71',
        'external': '#95a5a6'
    }
    
    # Title
    ax.text(8, 11.5, 'Grievance Management System - Architecture', 
            fontsize=20, fontweight='bold', ha='center')
    ax.text(8, 11, 'Professional AI-Powered Complaint Management Platform', 
            fontsize=12, ha='center', style='italic')
    
    # User Layer
    user_box = FancyBboxPatch((1, 9.5), 3, 1, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['external'], 
                              edgecolor='black', linewidth=2)
    ax.add_patch(user_box)
    ax.text(2.5, 10, 'Users\n(Web Browser)', fontsize=10, ha='center', va='center', fontweight='bold')
    
    # Admin Layer
    admin_box = FancyBboxPatch((12, 9.5), 3, 1, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['external'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(admin_box)
    ax.text(13.5, 10, 'Administrators\n(Admin Panel)', fontsize=10, ha='center', va='center', fontweight='bold')
    
    # Load Balancer / Reverse Proxy
    nginx_box = FancyBboxPatch((6.5, 8.5), 3, 0.8, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['infrastructure'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(nginx_box)
    ax.text(8, 8.9, 'Nginx Reverse Proxy\n(Load Balancer)', fontsize=9, ha='center', va='center', fontweight='bold')
    
    # Frontend Layer
    streamlit_box = FancyBboxPatch((1, 7), 6, 1, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['frontend'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(streamlit_box)
    ax.text(4, 7.5, 'Frontend Layer - Streamlit\n• User Interface • Admin Dashboard • Real-time Chat', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # API Gateway
    api_box = FancyBboxPatch((9, 7), 6, 1, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['backend'], 
                             edgecolor='black', linewidth=2)
    ax.add_patch(api_box)
    ax.text(12, 7.5, 'API Layer - FastAPI\n• REST Endpoints • Request Validation • Response Formatting', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # Business Logic Layer
    business_box = FancyBboxPatch((1, 5.5), 7, 1, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['backend'], 
                                  edgecolor='black', linewidth=2)
    ax.add_patch(business_box)
    ax.text(4.5, 6, 'Business Logic Layer\n• Complaint Processing • Status Management • Validation • Workflow', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # AI/ML Layer
    ai_box = FancyBboxPatch((9, 5.5), 6, 1, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['ai'], 
                            edgecolor='black', linewidth=2)
    ax.add_patch(ai_box)
    ax.text(12, 6, 'AI/ML Layer\n• RAG System • LLM Integration • Context Analysis • Smart Responses', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # Data Access Layer
    data_access_box = FancyBboxPatch((1, 4), 14, 0.8, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['database'], 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(data_access_box)
    ax.text(8, 4.4, 'Data Access Layer - Database Manager\n• ORM • Connection Pooling • Query Optimization • Transaction Management', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # Database Layer
    db_box = FancyBboxPatch((3, 2.5), 4, 1, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['database'], 
                            edgecolor='black', linewidth=2)
    ax.add_patch(db_box)
    ax.text(5, 3, 'SQLite Database\n• Complaints • Users • Status History', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # Vector Database
    vector_box = FancyBboxPatch((9, 2.5), 4, 1, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['ai'], 
                                edgecolor='black', linewidth=2)
    ax.add_patch(vector_box)
    ax.text(11, 3, 'Vector Store\n• Embeddings • Knowledge Base • RAG Context', 
            fontsize=9, ha='center', va='center', fontweight='bold', color='white')
    
    # External Services
    groq_box = FancyBboxPatch((1, 1), 3, 0.8, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['external'], 
                              edgecolor='black', linewidth=2)
    ax.add_patch(groq_box)
    ax.text(2.5, 1.4, 'Groq LLM API\n(External Service)', fontsize=8, ha='center', va='center', fontweight='bold')
    
    # Docker Container
    docker_box = FancyBboxPatch((12, 1), 3, 0.8, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['infrastructure'], 
                                edgecolor='black', linewidth=2)
    ax.add_patch(docker_box)
    ax.text(13.5, 1.4, 'Docker Container\n(Deployment)', fontsize=8, ha='center', va='center', fontweight='bold')
    
    # Add arrows for data flow
    arrows = [
        # User to Nginx
        ((2.5, 9.5), (8, 9.3)),
        ((13.5, 9.5), (8, 9.3)),
        # Nginx to Frontend/API
        ((8, 8.5), (4, 8)),
        ((8, 8.5), (12, 8)),
        # Frontend to Business Logic
        ((4, 7), (4.5, 6.5)),
        # API to Business Logic
        ((12, 7), (4.5, 6.5)),
        # Business Logic to AI
        ((8, 6), (9, 6)),
        # Business Logic to Data Access
        ((4.5, 5.5), (8, 4.8)),
        # AI to Data Access
        ((12, 5.5), (8, 4.8)),
        # Data Access to Databases
        ((8, 4), (5, 3.5)),
        ((8, 4), (11, 3.5)),
        # AI to External LLM
        ((9, 5.5), (4, 1.8)),
    ]
    
    for start, end in arrows:
        arrow = ConnectionPatch(start, end, "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc="black", alpha=0.7)
        ax.add_patch(arrow)
    
    # Add component details
    ax.text(0.5, 0.5, 'Key Features:\n• RAG-based AI responses\n• Real-time status tracking\n• Admin management\n• Docker deployment\n• Professional UI/UX', 
            fontsize=8, va='bottom', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    
    ax.text(10, 0.5, 'Technology Stack:\n• Python/FastAPI\n• Streamlit\n• SQLite\n• Docker\n• Nginx\n• Groq LLM', 
            fontsize=8, va='bottom', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('architecture_diagram.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('architecture_diagram.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("✅ Architecture diagram saved as 'architecture_diagram.png' and 'architecture_diagram.pdf'")
    return fig

def create_data_flow_diagram():
    """Create data flow diagram"""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Title
    ax.text(7, 9.5, 'Data Flow Diagram - Complaint Processing', 
            fontsize=18, fontweight='bold', ha='center')
    
    # Process boxes
    processes = [
        (2, 8, "User Input\n(Complaint)"),
        (7, 8, "AI Processing\n(RAG + LLM)"),
        (12, 8, "Response\nGeneration"),
        (2, 6, "Data Extraction\n(Name, Mobile, Details)"),
        (7, 6, "Validation &\nCategorization"),
        (12, 6, "Database\nStorage"),
        (2, 4, "Status\nTracking"),
        (7, 4, "Admin\nManagement"),
        (12, 4, "Notification\nSystem"),
        (7, 2, "Analytics &\nReporting")
    ]
    
    for x, y, label in processes:
        box = FancyBboxPatch((x-0.8, y-0.4), 1.6, 0.8, 
                             boxstyle="round,pad=0.1", 
                             facecolor='lightblue', 
                             edgecolor='navy', linewidth=1.5)
        ax.add_patch(box)
        ax.text(x, y, label, fontsize=9, ha='center', va='center', fontweight='bold')
    
    # Data flows
    flows = [
        ((2, 7.6), (7, 8.4)),  # User to AI
        ((7, 7.6), (12, 8.4)),  # AI to Response
        ((2, 7.6), (2, 6.4)),   # User to Extraction
        ((2, 5.6), (7, 6.4)),   # Extraction to Validation
        ((7, 5.6), (12, 6.4)),  # Validation to Storage
        ((12, 5.6), (2, 4.4)),  # Storage to Status
        ((2, 3.6), (7, 4.4)),   # Status to Admin
        ((7, 3.6), (12, 4.4)),  # Admin to Notification
        ((7, 3.6), (7, 2.4)),   # Admin to Analytics
    ]
    
    for start, end in flows:
        arrow = ConnectionPatch(start, end, "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=15, fc="red", alpha=0.7)
        ax.add_patch(arrow)
    
    plt.tight_layout()
    plt.savefig('data_flow_diagram.png', dpi=300, bbox_inches='tight')
    print("✅ Data flow diagram saved as 'data_flow_diagram.png'")
    return fig

def main():
    """Generate all diagrams"""
    print("🎨 Generating Architecture Diagrams...")
    
    # Create architecture diagram
    arch_fig = create_architecture_diagram()
    
    # Create data flow diagram
    flow_fig = create_data_flow_diagram()
    
    print("\n📊 Architecture diagrams generated successfully!")
    print("📁 Files created:")
    print("   • architecture_diagram.png")
    print("   • architecture_diagram.pdf")
    print("   • data_flow_diagram.png")
    
    # Show the diagrams
    plt.show()

if __name__ == "__main__":
    main()
