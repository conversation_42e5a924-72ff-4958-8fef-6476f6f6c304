# Groq API Configuration
GROQ_API_KEY=********************************************************

# Database Configuration (optional - defaults to SQLite)
DATABASE_URL=grievance_system.db

# API Configuration (optional - defaults shown)
API_HOST=127.0.0.1
API_PORT=8000

# Application Configuration
APP_TITLE=Grievance Management Chatbot
APP_DESCRIPTION=RAG-based chatbot for complaint registration and status tracking

# Instructions:
# 1. Copy this file to .env
# 2. Replace 'your_groq_api_key_here' with your actual Groq API key
# 3. Get your Groq API key from: https://console.groq.com/keys
# 4. Keep other settings as default unless you need to change them
