#!/bin/bash

# Grievance Management System - Docker Runner
# This script builds and runs the Docker container

set -e

echo "🐳 Grievance Management System - Docker Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

print_info "Docker is available and running"

# Stop and remove existing container if it exists
if docker ps -a --format 'table {{.Names}}' | grep -q grievance-management-system; then
    print_info "Stopping existing container..."
    docker stop grievance-management-system 2>/dev/null || true
    docker rm grievance-management-system 2>/dev/null || true
fi

# Build the Docker image
print_info "Building Docker image..."
docker build -t grievance-management-system .

if [ $? -eq 0 ]; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Run the container
print_info "Starting the container..."
docker run -d \
    --name grievance-management-system \
    -p 8000:8000 \
    -p 8501:8501 \
    -v "$(pwd)/data:/app/data" \
    -v "$(pwd)/logs:/app/logs" \
    --restart unless-stopped \
    grievance-management-system

if [ $? -eq 0 ]; then
    print_status "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for services to start
print_info "Waiting for services to start..."
sleep 10

# Check if services are running
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ || echo "000")
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8501/ || echo "000")

echo ""
echo "🎉 GRIEVANCE MANAGEMENT SYSTEM STARTED!"
echo "========================================"

if [ "$API_STATUS" = "200" ]; then
    print_status "API Server: Running (http://localhost:8000)"
else
    print_warning "API Server: Starting... (may take a moment)"
fi

if [ "$FRONTEND_STATUS" = "200" ]; then
    print_status "Frontend: Running (http://localhost:8501)"
else
    print_warning "Frontend: Starting... (may take a moment)"
fi

echo ""
echo "🌐 Access URLs:"
echo "   • Main Application: http://localhost:8501"
echo "   • Admin Panel: http://localhost:8501/admin"
echo "   • API Documentation: http://localhost:8000/docs"
echo ""
echo "👨‍💼 Admin Credentials:"
echo "   • Username: admin"
echo "   • Password: admin123"
echo ""
echo "🔧 Docker Commands:"
echo "   • View logs: docker logs grievance-management-system"
echo "   • Stop system: docker stop grievance-management-system"
echo "   • Start system: docker start grievance-management-system"
echo "   • Remove system: docker rm grievance-management-system"
echo ""
print_status "System is ready for use!"
