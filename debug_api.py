#!/usr/bin/env python3
"""
Debug API registration issue
"""

import sys
import traceback
sys.path.append('src')

def test_complaint_registration():
    """Test complaint registration directly"""
    try:
        print("🧪 Testing complaint registration components...")
        
        # Test models
        from models.models import ComplaintRequest, ComplaintCategory
        print("✅ Models imported successfully")
        
        # Test database
        from database.database import DatabaseManager
        db = DatabaseManager()
        db.init_database()
        print("✅ Database initialized successfully")
        
        # Test RAG system
        from core.rag_system import RAGSystem
        rag = RAGSystem()
        print("✅ RAG system initialized successfully")
        
        # Create test complaint
        complaint = ComplaintRequest(
            name="Debug Test",
            mobile="1234567890",
            email="<EMAIL>",
            complaint_details="Testing complaint registration with email",
            category=ComplaintCategory.OTHER
        )
        print("✅ Complaint request created successfully")
        
        # Test RAG response
        context = rag.get_contextual_response(complaint.complaint_details)
        print(f"✅ RAG context generated: {context.get('category', 'Unknown')}")
        
        # Test database registration
        result = db.register_complaint(complaint)
        print(f"✅ Complaint registered: {result.complaint_id}")
        print(f"✅ Email stored: {result.email}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"❌ Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_complaint_registration()
    if success:
        print("\n🎉 All components working correctly!")
    else:
        print("\n❌ Issues found in components!")
