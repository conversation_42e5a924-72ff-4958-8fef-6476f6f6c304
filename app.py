import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional
import time
import re

from models import UserContext, ComplaintRequest, ComplaintCategory
from llm_handler import LLMHandler
from config import Config

# Page configuration
st.set_page_config(
    page_title="Grievance Management Chatbot",
    page_icon="🎧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_context" not in st.session_state:
    st.session_state.user_context = UserContext()
if "llm_handler" not in st.session_state:
    try:
        st.session_state.llm_handler = LLMHandler()
    except Exception as e:
        st.error(f"Could not initialize LLM handler: {e}")
        st.session_state.llm_handler = None

def check_api_server():
    """Check if API server is running"""
    try:
        response = requests.get(f"{Config.API_BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def validate_mobile(mobile: str) -> bool:
    """Validate mobile number format"""
    pattern = r"^\+?[1-9]\d{9,14}$"
    return bool(re.match(pattern, mobile.strip()))

def validate_name(name: str) -> bool:
    """Validate name format"""
    return len(name.strip()) >= 2 and name.strip().replace(" ", "").isalpha()

def register_complaint_api(name: str, mobile: str, complaint_details: str, category: str = "Other") -> Dict[str, Any]:
    """Call API to register complaint"""
    try:
        complaint_data = {
            "name": name,
            "mobile": mobile,
            "complaint_details": complaint_details,
            "category": category
        }
        
        response = requests.post(
            f"{Config.API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_complaint_status_api(complaint_id: str) -> Dict[str, Any]:
    """Get complaint status from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/complaint-status/{complaint_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        elif response.status_code == 404:
            return {"success": False, "error": "Complaint not found"}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_user_complaints_api(mobile: str) -> Dict[str, Any]:
    """Get user complaints from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/user-complaints/{mobile}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def process_user_message(user_message: str) -> str:
    """Process user message and generate response"""
    context = st.session_state.user_context
    llm_handler = st.session_state.llm_handler

    # Simple rule-based intent detection (fallback)
    message_lower = user_message.lower()

    # Determine intent based on keywords and context
    if context.current_step != "initial":
        # User is in middle of complaint registration
        return handle_complaint_registration(user_message, context, "provide_info", {})
    elif any(word in message_lower for word in ["complaint", "complain", "issue", "problem", "register", "file", "report"]):
        return handle_complaint_registration(user_message, context, "register_complaint", {})
    elif any(word in message_lower for word in ["status", "check", "update", "progress", "what's", "how's"]):
        return handle_status_check(user_message, context, {})
    else:
        # General greeting or help
        return get_general_response(user_message, context)

def handle_complaint_registration(user_message: str, context: UserContext, intent: str, extracted_info: Dict) -> str:
    """Handle complaint registration flow"""

    # Step 1: Collect name
    if not context.name:
        if context.current_step == "initial":
            context.current_step = "collecting_name"
            return """📝 **Complaint Registration Process**

I'll help you register your complaint. This process involves 3 simple steps:
1. Your full name
2. Your mobile number
3. Complaint details

Let's start - **Please provide your full name:**"""
        elif context.current_step == "collecting_name":
            name = user_message.strip()
            if validate_name(name):
                context.name = name
                context.current_step = "collecting_mobile"
                return f"""✅ Thank you, **{name}**!

**Step 2 of 3:** Please provide your mobile number (with country code if international):"""
            else:
                return """❌ **Invalid Name Format**

Please provide a valid name:
• At least 2 characters
• Letters and spaces only
• Example: "John Doe" or "Sarah Smith"

**Please enter your full name:**"""
    
    # Step 2: Collect mobile
    elif not context.mobile:
        if context.current_step == "collecting_mobile":
            mobile = user_message.strip()
            if validate_mobile(mobile):
                context.mobile = mobile
                context.current_step = "collecting_details"
                return f"""✅ Mobile number **{mobile}** recorded successfully!

**Step 3 of 3:** Please describe your complaint in detail:

• What is the issue you're facing?
• When did it start?
• Any error messages or specific problems?

**Please provide your complaint details:**"""
            else:
                return """❌ **Invalid Mobile Number Format**

Please provide a valid mobile number:
• 10-15 digits
• Can include country code (+1, +91, etc.)
• Examples: "+1234567890" or "9876543210"

**Please enter your mobile number:**"""
    
    # Step 3: Collect complaint details
    elif not context.complaint_details:
        if context.current_step == "collecting_details":
            if len(user_message.strip()) < 10:
                return """❌ **Insufficient Details**

Please provide more detailed information about your complaint:
• Minimum 10 characters required
• Be specific about the issue
• Include relevant details like error messages, when it started, etc.

**Please describe your complaint in detail:**"""

            context.complaint_details = user_message.strip()
            context.current_step = "processing"
            
            # Register complaint via API
            # Use default category if LLM handler is not available
            if st.session_state.llm_handler:
                try:
                    category = st.session_state.llm_handler.categorize_complaint(context.complaint_details)
                    category_value = category.value
                except:
                    category_value = "Other"
            else:
                category_value = "Other"

            result = register_complaint_api(
                context.name,
                context.mobile,
                context.complaint_details,
                category_value
            )
            
            if result["success"]:
                complaint_data = result["data"]
                complaint_id = complaint_data["complaint_id"]

                # Reset context for next interaction
                st.session_state.user_context = UserContext()

                return f"""🎉 **Complaint Registered Successfully!**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**📋 COMPLAINT DETAILS**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**🆔 Complaint ID:** `{complaint_id}`
**👤 Name:** {context.name}
**📱 Mobile:** {context.mobile}
**📊 Status:** {complaint_data['status']}
**🏷️ Category:** {complaint_data['category']}
**📅 Registered:** {complaint_data['created_at'][:10]}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📌 IMPORTANT NOTES:**
• **Save your Complaint ID:** `{complaint_id}`
• **Expected Resolution:** 2-5 business days
• **Status Updates:** Check anytime with your Complaint ID
• **Reference Number:** Keep this for all future communications

**🔍 To Check Status:** Simply say "Check status" and provide your Complaint ID

Thank you for using our Grievance Management System. We'll resolve your issue promptly!

Is there anything else I can help you with?"""
            else:
                # Reset context on error too
                st.session_state.user_context = UserContext()
                return f"""❌ **Registration Failed**

We encountered an error while registering your complaint:
**Error:** {result['error']}

**Please try again by saying:** "I want to register a complaint"

If the problem persists, please contact our support team."""
    
    return "I'm processing your request. Please wait..."

def handle_status_check(user_message: str, context: UserContext, extracted_info: Dict) -> str:
    """Handle status check requests"""
    
    # Look for complaint ID in the message
    complaint_id_pattern = r"CMP[A-Z0-9]{8}"
    complaint_id_match = re.search(complaint_id_pattern, user_message.upper())
    
    if complaint_id_match:
        complaint_id = complaint_id_match.group()
        result = get_complaint_status_api(complaint_id)
        
        if result["success"]:
            complaint = result["data"]

            # Status-specific messages
            status_messages = {
                "Registered": "📝 Your complaint has been received and is in our system",
                "In Progress": "⚙️ Our team is actively working on your complaint",
                "Under Review": "🔍 Your complaint is being reviewed by our specialists",
                "Resolved": "✅ Your complaint has been resolved successfully",
                "Closed": "📁 Your complaint has been closed",
                "Rejected": "❌ Your complaint could not be processed"
            }

            status_msg = status_messages.get(complaint['status'], "📊 Status updated")

            return f"""📋 **COMPLAINT STATUS REPORT**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**🆔 Complaint ID:** `{complaint['complaint_id']}`
**📊 Current Status:** **{complaint['status']}**
**🏷️ Category:** {complaint['category']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📅 Timeline:**
• **Registered:** {complaint['created_at'][:10]}
• **Last Updated:** {complaint['updated_at'][:10]}

**📝 Complaint Details:**
{complaint['complaint_details'][:150]}{"..." if len(complaint['complaint_details']) > 150 else ""}

**ℹ️ Status Information:**
{status_msg}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Need help with anything else? You can:
• Register a new complaint
• Check another complaint status"""
        else:
            return f"""❌ **Complaint Not Found**

**Error:** {result['error']}

**Please verify:**
• Complaint ID format: CMP12345678
• Check for typos in the ID
• Ensure the complaint was registered in this system

**Need help?** Say "help" for assistance or try registering a new complaint."""
    
    # Look for mobile number
    elif "mobile" in extracted_info or validate_mobile(user_message.strip()):
        mobile = extracted_info.get("mobile", user_message.strip())
        result = get_user_complaints_api(mobile)
        
        if result["success"]:
            complaints = result["data"]
            if complaints:
                response = f"""📱 **COMPLAINTS SUMMARY**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**📞 Mobile Number:** {mobile}
**📊 Total Complaints:** {len(complaints)}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📋 Recent Complaints:**
"""
                for i, complaint in enumerate(complaints[:5], 1):  # Show latest 5
                    status_emoji = {
                        "Registered": "📝",
                        "In Progress": "⚙️",
                        "Under Review": "🔍",
                        "Resolved": "✅",
                        "Closed": "📁",
                        "Rejected": "❌"
                    }.get(complaint['status'], "📊")

                    response += f"""
**{i}.** `{complaint['complaint_id']}` {status_emoji}
   **Status:** {complaint['status']}
   **Date:** {complaint['created_at'][:10]}
   **Category:** {complaint['category']}"""

                response += f"""

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**🔍 For detailed status:** Provide a specific Complaint ID
**📝 To register new complaint:** Say "I have a complaint"

Which complaint would you like to check in detail?"""
                return response
            else:
                return f"""📱 **No Complaints Found**

**Mobile Number:** {mobile}

**This could mean:**
• No complaints registered with this number
• Different mobile number was used during registration
• Typo in the mobile number

**Would you like to:**
• Register a new complaint?
• Try a different mobile number?
• Check with a Complaint ID instead?"""
        else:
            return f"""❌ **Error Fetching Complaints**

**Error:** {result['error']}

**Please try:**
• Check your mobile number format
• Ensure you're using the registered number
• Contact support if the issue persists"""
    
    else:
        return """🔍 **Status Check Required**

To check your complaint status, please provide **one of the following:**

**Option 1: Complaint ID**
• Format: `CMP12345678`
• Example: "Check status CMP9B41CA0F"
• Most accurate method

**Option 2: Mobile Number**
• Use the number you registered with
• Example: "+1234567890" or "9876543210"
• Shows all your complaints

**Sample Commands:**
• "Status of CMP9B41CA0F"
• "Check my complaints for +1234567890"
• "What's the status of my complaint CMP12345678"

Please provide your Complaint ID or mobile number to proceed."""

def get_general_response(user_message: str, context: UserContext) -> str:
    """Generate general responses for greetings and help"""
    message_lower = user_message.lower()

    if any(word in message_lower for word in ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]):
        return """👋 **Hello! Welcome to the Grievance Management System**

I'm your AI assistant, designed to help you efficiently manage your complaints and inquiries.

**🎯 What I can do for you:**

**🆕 Complaint Registration**
• Guide you through the registration process
• Collect necessary information step-by-step
• Generate unique tracking IDs

**🔍 Status Tracking**
• Check complaint status instantly
• View detailed progress updates
• Access complete complaint history

**💡 Quick Start:**
• Type "I have a complaint" to begin registration
• Type "Check status" to track existing complaints
• Type "Help" for detailed instructions

How may I assist you today?"""

    elif any(word in message_lower for word in ["help", "what can you do", "how does this work", "guide", "instructions"]):
        return """📚 **Comprehensive Help Guide**

**🆕 REGISTERING A NEW COMPLAINT**

**Step 1:** Initiate Registration
• Say: "I have a complaint" or "Register complaint"
• I'll guide you through a 3-step process

**Step 2:** Provide Information
• **Name:** Your full legal name
• **Mobile:** Contact number (with country code if international)
• **Details:** Comprehensive description of your issue

**Step 3:** Receive Confirmation
• Get unique Complaint ID (format: CMP12345678)
• Save this ID for future reference
• Estimated resolution: 2-5 business days

---

**🔍 CHECKING COMPLAINT STATUS**

**Method 1:** By Complaint ID
• Say: "Check status CMP12345678"
• Most accurate and fastest method

**Method 2:** By Mobile Number
• Say: "Check complaints for +1234567890"
• Shows all complaints for that number

---

**📋 SAMPLE COMMANDS**
• "I want to file a complaint about my laptop"
• "What's the status of CMP9B41CA0F?"
• "Check all my complaints for +1234567890"
• "Help me register a new complaint"

---

**🎯 TIPS FOR BEST EXPERIENCE**
• Be specific when describing issues
• Keep your Complaint ID safe
• Check status regularly for updates
• Provide accurate contact information

What would you like to do first?"""

    elif any(word in message_lower for word in ["thank", "thanks", "bye", "goodbye", "exit", "quit"]):
        return """🙏 **Thank you for using our Grievance Management System!**

**📋 Session Summary:**
• Professional complaint handling
• Secure data management
• 24/7 AI assistance available

**🔔 Remember:**
• Keep your Complaint IDs safe
• Check back for status updates
• We're here whenever you need help

**Have a wonderful day!** 🌟

*Feel free to return anytime for assistance with your complaints or to register new ones.*"""

    else:
        return """🤖 **AI Grievance Assistant Ready**

I'm here to provide professional complaint management services.

**🎯 Primary Functions:**

**🆕 New Complaint Registration**
• Streamlined 3-step process
• Instant ID generation
• Professional handling

**🔍 Status & Tracking Services**
• Real-time status updates
• Complete complaint history
• Multi-search options

**💬 Natural Language Processing**
• Understand your requests naturally
• Context-aware responses
• Professional communication

**🚀 Quick Actions:**
• Say "complaint" to register
• Say "status" to check progress
• Say "help" for detailed guidance

What can I help you with today?"""

# Main UI
def main():
    # Custom CSS for ChatGPT-like styling
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    .chat-container {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }

    .user-message {
        background-color: #007bff;
        color: white;
        padding: 0.8rem;
        border-radius: 18px;
        margin: 0.5rem 0;
        max-width: 80%;
        margin-left: auto;
    }

    .assistant-message {
        background-color: #f1f3f4;
        color: #333;
        padding: 0.8rem;
        border-radius: 18px;
        margin: 0.5rem 0;
        max-width: 80%;
        border-left: 4px solid #667eea;
    }

    .quick-action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        margin: 0.2rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-online {
        background-color: #28a745;
        animation: pulse 2s infinite;
    }

    .status-offline {
        background-color: #dc3545;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .sidebar-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #667eea;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header with gradient styling
    st.markdown("""
    <div class="main-header">
        <h1>🎧 Professional Grievance Management System</h1>
        <p style="font-size: 1.2em; margin: 0;">AI-Powered Complaint Registration & Status Tracking</p>
        <p style="font-size: 0.9em; margin: 0.5rem 0 0 0;">Powered by Advanced RAG Technology & Natural Language Processing</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar with enhanced styling
    with st.sidebar:
        # System Status with visual indicators
        st.markdown("### 📊 System Status")

        api_status = check_api_server()
        if api_status:
            st.markdown("""
            <div class="sidebar-section">
                <span class="status-indicator status-online"></span>
                <strong>API Server: Online</strong><br>
                <small>All services operational</small>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="sidebar-section">
                <span class="status-indicator status-offline"></span>
                <strong>API Server: Offline</strong><br>
                <small>Please start the server</small>
            </div>
            """, unsafe_allow_html=True)
            st.code("python api_server.py", language="bash")

        st.markdown("---")
        st.markdown("### 📋 Quick Guide")

        with st.expander("🆕 Register New Complaint"):
            st.markdown("""
            **Steps:**
            1. Say "I have a complaint"
            2. Provide your full name
            3. Enter mobile number
            4. Describe the issue
            5. Get your Complaint ID
            """)

        with st.expander("🔍 Check Complaint Status"):
            st.markdown("""
            **Methods:**
            • **By ID:** Provide Complaint ID (CMP12345678)
            • **By Mobile:** Enter registered mobile number

            **Sample Commands:**
            • "Check status CMP9B41CA0F"
            • "Status for +1234567890"
            """)

        st.markdown("---")
        st.header("🛠️ Actions")

        if st.button("🔄 Clear Chat History", use_container_width=True):
            st.session_state.messages = []
            st.session_state.user_context = UserContext()
            st.rerun()

        if st.button("📊 View Sample Data", use_container_width=True):
            st.info("""
            **Sample Complaint IDs to test:**
            • CMP9B41CA0F
            • CMPBD678C56
            • CMP3A066B16
            """)

        st.markdown("---")
        st.markdown("**💡 Tips:**")
        st.markdown("• Keep your Complaint ID safe")
        st.markdown("• Check status regularly")
        st.markdown("• Provide detailed descriptions")
    
    # Welcome message for new users
    if not st.session_state.messages:
        welcome_msg = """👋 **Welcome to the Professional Grievance Management System!**

I'm your AI assistant, ready to help you with:

🆕 **Register New Complaints**
🔍 **Check Complaint Status**
📊 **Track Your Issues**

**To get started, try saying:**
• "I have a complaint to register"
• "Check my complaint status"
• "Help me with my issue"

How can I assist you today?"""

        st.session_state.messages.append({"role": "assistant", "content": welcome_msg})

        with st.chat_message("assistant", avatar="🤖"):
            st.markdown(welcome_msg)

    # Chat interface with improved styling
    st.markdown("### 💬 Chat Interface")

    # Create a container for chat messages with custom styling
    chat_container = st.container()

    with chat_container:
        # Display chat messages with avatars and better formatting
        for i, message in enumerate(st.session_state.messages):
            if message["role"] == "user":
                with st.chat_message("user", avatar="👤"):
                    # Add timestamp for user messages
                    st.caption(f"You • {datetime.now().strftime('%H:%M')}")
                    st.markdown(f"**{message['content']}**")
            else:
                with st.chat_message("assistant", avatar="🤖"):
                    # Add timestamp for assistant messages
                    st.caption(f"AI Assistant • {datetime.now().strftime('%H:%M')}")
                    st.markdown(message["content"])

        # Chat input with enhanced placeholder and suggestions
        placeholder_text = "💬 Type your message here..."

        # Enhanced quick action buttons with better styling
        st.markdown("### 🚀 Quick Actions")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("🆕 New Complaint", use_container_width=True, help="Start registering a new complaint"):
                prompt = "I want to register a complaint"
                st.session_state.quick_action = prompt
        with col2:
            if st.button("🔍 Check Status", use_container_width=True, help="Check existing complaint status"):
                prompt = "Check my complaint status"
                st.session_state.quick_action = prompt
        with col3:
            if st.button("📋 View Sample", use_container_width=True, help="See sample complaint data"):
                prompt = "Show me sample complaint CMP9B41CA0F"
                st.session_state.quick_action = prompt
        with col4:
            if st.button("❓ Help", use_container_width=True, help="Get detailed help and instructions"):
                prompt = "Help me understand how this works"
                st.session_state.quick_action = prompt

        # Add suggested prompts
        st.markdown("### 💡 Suggested Prompts")
        suggestions = [
            "I have an issue with my laptop",
            "Check status for mobile +1234567890",
            "What's the status of CMP9B41CA0F?",
            "How do I register a complaint?",
            "Show me all my complaints"
        ]

        selected_suggestion = st.selectbox(
            "Choose a sample prompt or type your own:",
            [""] + suggestions,
            help="Select a pre-written prompt or type your own message below"
        )

        if selected_suggestion:
            st.session_state.quick_action = selected_suggestion

        # Handle quick actions
        if hasattr(st.session_state, 'quick_action'):
            prompt = st.session_state.quick_action
            del st.session_state.quick_action
        else:
            prompt = st.chat_input(placeholder_text)

        if prompt:
            # Add user message with timestamp
            user_message = {
                "role": "user",
                "content": prompt,
                "timestamp": datetime.now().strftime('%H:%M')
            }
            st.session_state.messages.append(user_message)

            # Display user message immediately
            with st.chat_message("user", avatar="👤"):
                st.caption(f"You • {user_message['timestamp']}")
                st.markdown(f"**{prompt}**")

            # Generate and display assistant response with typing effect
            with st.chat_message("assistant", avatar="🤖"):
                st.caption(f"AI Assistant • {datetime.now().strftime('%H:%M')}")

                # Professional typing indicator
                typing_placeholder = st.empty()

                # Show animated typing indicator
                typing_placeholder.markdown("""
                <div style="display: flex; align-items: center; padding: 10px;">
                    <div style="margin-right: 10px;">🤖</div>
                    <div style="display: flex; align-items: center;">
                        <span>AI is thinking</span>
                        <div style="margin-left: 10px;">
                            <span style="animation: blink 1.4s infinite both;">.</span>
                            <span style="animation: blink 1.4s infinite both; animation-delay: 0.2s;">.</span>
                            <span style="animation: blink 1.4s infinite both; animation-delay: 0.4s;">.</span>
                        </div>
                    </div>
                </div>
                <style>
                @keyframes blink {
                    0%, 80%, 100% { opacity: 0; }
                    40% { opacity: 1; }
                }
                </style>
                """, unsafe_allow_html=True)

                # Process the request
                if not api_status:
                    response = """❌ **System Temporarily Unavailable**

I apologize, but our backend services are currently offline. This means I cannot process complaint registrations or status checks at the moment.

**🔧 Technical Details:**
• API server connection failed
• Database services unavailable
• Real-time processing disabled

**🚀 Quick Resolution:**
1. Ensure the API server is running: `python api_server.py`
2. Verify server accessibility at http://127.0.0.1:8000
3. Check system logs for error details

**💡 Alternative Actions:**
• Try again in a few moments
• Contact system administrator
• Use offline complaint forms if available

I'll be ready to help once the system is back online!"""
                else:
                    response = process_user_message(prompt)

                # Clear typing indicator
                typing_placeholder.empty()

                # Display response with professional formatting
                response_container = st.container()
                with response_container:
                    # Add response with smooth appearance
                    st.markdown(f"""
                    <div style="
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        padding: 1.5rem;
                        border-radius: 15px;
                        border-left: 4px solid #667eea;
                        margin: 1rem 0;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        animation: slideIn 0.3s ease-out;
                    ">
                        {response}
                    </div>
                    <style>
                    @keyframes slideIn {{
                        from {{ opacity: 0; transform: translateY(10px); }}
                        to {{ opacity: 1; transform: translateY(0); }}
                    }}
                    </style>
                    """, unsafe_allow_html=True)

                # Add to message history
                assistant_message = {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().strftime('%H:%M')
                }
                st.session_state.messages.append(assistant_message)

    # Professional Footer
    st.markdown("---")
    st.markdown("""
    <div style="
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        margin-top: 3rem;
    ">
        <h4 style="margin: 0 0 1rem 0;">🎧 Professional Grievance Management System</h4>
        <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap; margin-bottom: 1rem;">
            <div>
                <strong>🤖 AI-Powered</strong><br>
                <small>Advanced NLP & RAG Technology</small>
            </div>
            <div>
                <strong>🔒 Secure</strong><br>
                <small>Enterprise-Grade Data Protection</small>
            </div>
            <div>
                <strong>⚡ Fast</strong><br>
                <small>Real-Time Processing & Updates</small>
            </div>
            <div>
                <strong>📱 Accessible</strong><br>
                <small>24/7 Availability</small>
            </div>
        </div>
        <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 1rem 0;">
        <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">
            © 2024 Cyfuture Assignment | Built with Streamlit, FastAPI & Groq LLM
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Add some spacing at the bottom
    st.markdown("<br><br>", unsafe_allow_html=True)

if __name__ == "__main__":
    main()
