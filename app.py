import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional
import time
import re

from models import UserContext, ComplaintRequest, ComplaintCategory
from llm_handler import LLMHandler
from config import Config

# Page configuration
st.set_page_config(
    page_title="Grievance Management Chatbot",
    page_icon="🎧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_context" not in st.session_state:
    st.session_state.user_context = UserContext()
if "llm_handler" not in st.session_state:
    st.session_state.llm_handler = LLMHandler()

def check_api_server():
    """Check if API server is running"""
    try:
        response = requests.get(f"{Config.API_BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def validate_mobile(mobile: str) -> bool:
    """Validate mobile number format"""
    pattern = r"^\+?[1-9]\d{9,14}$"
    return bool(re.match(pattern, mobile.strip()))

def validate_name(name: str) -> bool:
    """Validate name format"""
    return len(name.strip()) >= 2 and name.strip().replace(" ", "").isalpha()

def register_complaint_api(name: str, mobile: str, complaint_details: str, category: str = "Other") -> Dict[str, Any]:
    """Call API to register complaint"""
    try:
        complaint_data = {
            "name": name,
            "mobile": mobile,
            "complaint_details": complaint_details,
            "category": category
        }
        
        response = requests.post(
            f"{Config.API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_complaint_status_api(complaint_id: str) -> Dict[str, Any]:
    """Get complaint status from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/complaint-status/{complaint_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        elif response.status_code == 404:
            return {"success": False, "error": "Complaint not found"}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_user_complaints_api(mobile: str) -> Dict[str, Any]:
    """Get user complaints from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/user-complaints/{mobile}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def process_user_message(user_message: str) -> str:
    """Process user message and generate response"""
    context = st.session_state.user_context
    llm_handler = st.session_state.llm_handler
    
    # Extract intent and information
    intent_data = llm_handler.extract_intent(user_message, context)
    intent = intent_data.get("intent", "general")
    extracted_info = intent_data.get("extracted_info", {})
    
    # Update context with extracted information
    if "name" in extracted_info:
        context.name = extracted_info["name"]
    if "mobile" in extracted_info:
        context.mobile = extracted_info["mobile"]
    if "complaint_details" in extracted_info:
        context.complaint_details = extracted_info["complaint_details"]
    
    # Process based on intent and current step
    if intent == "register_complaint" or context.current_step != "initial":
        return handle_complaint_registration(user_message, context, intent, extracted_info)
    elif intent == "check_status":
        return handle_status_check(user_message, context, extracted_info)
    else:
        # Generate general response
        response = llm_handler.generate_response(user_message, context, intent_data)
        return response

def handle_complaint_registration(user_message: str, context: UserContext, intent: str, extracted_info: Dict) -> str:
    """Handle complaint registration flow"""
    
    # Step 1: Collect name
    if not context.name:
        if context.current_step == "initial":
            context.current_step = "collecting_name"
            return "I'd be happy to help you register a complaint. Could you please provide your full name?"
        elif context.current_step == "collecting_name":
            name = user_message.strip()
            if validate_name(name):
                context.name = name
                context.current_step = "collecting_mobile"
                return f"Thank you, {name}! Now, could you please provide your mobile number?"
            else:
                return "Please provide a valid name (at least 2 characters, letters only)."
    
    # Step 2: Collect mobile
    elif not context.mobile:
        if context.current_step == "collecting_mobile":
            mobile = user_message.strip()
            if validate_mobile(mobile):
                context.mobile = mobile
                context.current_step = "collecting_details"
                return "Perfect! Now please describe your complaint in detail. What issue are you facing?"
            else:
                return "Please provide a valid mobile number (10-15 digits)."
    
    # Step 3: Collect complaint details
    elif not context.complaint_details:
        if context.current_step == "collecting_details":
            if len(user_message.strip()) < 10:
                return "Please provide more detailed information about your complaint (at least 10 characters)."
            
            context.complaint_details = user_message.strip()
            context.current_step = "processing"
            
            # Register complaint via API
            category = st.session_state.llm_handler.categorize_complaint(context.complaint_details)
            result = register_complaint_api(
                context.name, 
                context.mobile, 
                context.complaint_details,
                category.value
            )
            
            if result["success"]:
                complaint_data = result["data"]
                complaint_id = complaint_data["complaint_id"]
                
                # Reset context for next interaction
                st.session_state.user_context = UserContext()
                
                return f"""✅ Your complaint has been registered successfully!

**Complaint ID:** {complaint_id}
**Status:** {complaint_data['status']}
**Category:** {complaint_data['category']}

Please save this Complaint ID for future reference. You can check the status anytime by asking "What's the status of my complaint?" and providing this ID.

Is there anything else I can help you with?"""
            else:
                return f"❌ Sorry, there was an error registering your complaint: {result['error']}. Please try again."
    
    return "I'm processing your request. Please wait..."

def handle_status_check(user_message: str, context: UserContext, extracted_info: Dict) -> str:
    """Handle status check requests"""
    
    # Look for complaint ID in the message
    complaint_id_pattern = r"CMP[A-Z0-9]{8}"
    complaint_id_match = re.search(complaint_id_pattern, user_message.upper())
    
    if complaint_id_match:
        complaint_id = complaint_id_match.group()
        result = get_complaint_status_api(complaint_id)
        
        if result["success"]:
            complaint = result["data"]
            return f"""📋 **Complaint Status**

**Complaint ID:** {complaint['complaint_id']}
**Status:** {complaint['status']}
**Category:** {complaint['category']}
**Registered:** {complaint['created_at'][:10]}
**Last Updated:** {complaint['updated_at'][:10]}

**Details:** {complaint['complaint_details'][:100]}...

Is there anything else I can help you with?"""
        else:
            return f"❌ {result['error']}. Please check your Complaint ID and try again."
    
    # Look for mobile number
    elif "mobile" in extracted_info or validate_mobile(user_message.strip()):
        mobile = extracted_info.get("mobile", user_message.strip())
        result = get_user_complaints_api(mobile)
        
        if result["success"]:
            complaints = result["data"]
            if complaints:
                response = f"📱 **Your Complaints (Mobile: {mobile})**\n\n"
                for complaint in complaints[:5]:  # Show latest 5
                    response += f"**{complaint['complaint_id']}** - {complaint['status']} ({complaint['created_at'][:10]})\n"
                response += "\nTo get detailed status, please provide a specific Complaint ID."
                return response
            else:
                return f"No complaints found for mobile number {mobile}."
        else:
            return f"❌ Error fetching complaints: {result['error']}"
    
    else:
        return "To check your complaint status, please provide either:\n1. Your Complaint ID (format: CMP12345678)\n2. Your registered mobile number"

# Main UI
def main():
    st.title("🎧 Grievance Management Chatbot")
    st.markdown("*Powered by RAG and Groq LLM*")
    
    # Sidebar
    with st.sidebar:
        st.header("ℹ️ Information")
        
        # API Status
        api_status = check_api_server()
        if api_status:
            st.success("✅ API Server: Online")
        else:
            st.error("❌ API Server: Offline")
            st.warning("Please start the API server first:\n```bash\npython api_server.py\n```")
        
        st.markdown("---")
        st.markdown("**How to use:**")
        st.markdown("1. Say 'I have a complaint' to register")
        st.markdown("2. Provide name, mobile, and details")
        st.markdown("3. Get your Complaint ID")
        st.markdown("4. Check status anytime")
        
        st.markdown("---")
        if st.button("🔄 Clear Chat"):
            st.session_state.messages = []
            st.session_state.user_context = UserContext()
            st.rerun()
    
    # Chat interface
    chat_container = st.container()
    
    with chat_container:
        # Display chat messages
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
        
        # Chat input
        if prompt := st.chat_input("Type your message here..."):
            # Add user message
            st.session_state.messages.append({"role": "user", "content": prompt})
            
            with st.chat_message("user"):
                st.markdown(prompt)
            
            # Generate and display assistant response
            with st.chat_message("assistant"):
                with st.spinner("Thinking..."):
                    if not api_status:
                        response = "❌ API server is not running. Please start the server first."
                    else:
                        response = process_user_message(prompt)
                
                st.markdown(response)
                st.session_state.messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()
