import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional
import time
import re

from models import UserContext, ComplaintRequest, ComplaintCategory
from llm_handler import LLMHandler
from config import Config

# Page configuration
st.set_page_config(
    page_title="Grievance Management Chatbot",
    page_icon="🎧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_context" not in st.session_state:
    st.session_state.user_context = UserContext()
if "llm_handler" not in st.session_state:
    try:
        st.session_state.llm_handler = LLMHandler()
    except Exception as e:
        st.error(f"Could not initialize LLM handler: {e}")
        st.session_state.llm_handler = None

def check_api_server():
    """Check if API server is running"""
    try:
        response = requests.get(f"{Config.API_BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def validate_mobile(mobile: str) -> bool:
    """Validate mobile number format"""
    pattern = r"^\+?[1-9]\d{9,14}$"
    return bool(re.match(pattern, mobile.strip()))

def validate_name(name: str) -> bool:
    """Validate name format"""
    return len(name.strip()) >= 2 and name.strip().replace(" ", "").isalpha()

def register_complaint_api(name: str, mobile: str, complaint_details: str, category: str = "Other") -> Dict[str, Any]:
    """Call API to register complaint"""
    try:
        complaint_data = {
            "name": name,
            "mobile": mobile,
            "complaint_details": complaint_details,
            "category": category
        }
        
        response = requests.post(
            f"{Config.API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_complaint_status_api(complaint_id: str) -> Dict[str, Any]:
    """Get complaint status from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/complaint-status/{complaint_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        elif response.status_code == 404:
            return {"success": False, "error": "Complaint not found"}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def get_user_complaints_api(mobile: str) -> Dict[str, Any]:
    """Get user complaints from API"""
    try:
        response = requests.get(
            f"{Config.API_BASE_URL}/user-complaints/{mobile}",
            timeout=10
        )
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API Error: {response.status_code}"}
            
    except Exception as e:
        return {"success": False, "error": f"Connection error: {str(e)}"}

def process_user_message(user_message: str) -> str:
    """Process user message and generate response"""
    context = st.session_state.user_context
    llm_handler = st.session_state.llm_handler

    # Simple rule-based intent detection (fallback)
    message_lower = user_message.lower()

    # Determine intent based on keywords and context
    if context.current_step != "initial":
        # User is in middle of complaint registration
        return handle_complaint_registration(user_message, context, "provide_info", {})
    elif any(word in message_lower for word in ["complaint", "complain", "issue", "problem", "register", "file", "report"]):
        return handle_complaint_registration(user_message, context, "register_complaint", {})
    elif any(word in message_lower for word in ["status", "check", "update", "progress", "what's", "how's"]):
        return handle_status_check(user_message, context, {})
    else:
        # General greeting or help
        return get_general_response(user_message, context)

def handle_complaint_registration(user_message: str, context: UserContext, intent: str, extracted_info: Dict) -> str:
    """Handle complaint registration flow"""

    # Step 1: Collect name
    if not context.name:
        if context.current_step == "initial":
            context.current_step = "collecting_name"
            return """📝 **Complaint Registration Process**

I'll help you register your complaint. This process involves 3 simple steps:
1. Your full name
2. Your mobile number
3. Complaint details

Let's start - **Please provide your full name:**"""
        elif context.current_step == "collecting_name":
            name = user_message.strip()
            if validate_name(name):
                context.name = name
                context.current_step = "collecting_mobile"
                return f"""✅ Thank you, **{name}**!

**Step 2 of 3:** Please provide your mobile number (with country code if international):"""
            else:
                return """❌ **Invalid Name Format**

Please provide a valid name:
• At least 2 characters
• Letters and spaces only
• Example: "John Doe" or "Sarah Smith"

**Please enter your full name:**"""
    
    # Step 2: Collect mobile
    elif not context.mobile:
        if context.current_step == "collecting_mobile":
            mobile = user_message.strip()
            if validate_mobile(mobile):
                context.mobile = mobile
                context.current_step = "collecting_details"
                return f"""✅ Mobile number **{mobile}** recorded successfully!

**Step 3 of 3:** Please describe your complaint in detail:

• What is the issue you're facing?
• When did it start?
• Any error messages or specific problems?

**Please provide your complaint details:**"""
            else:
                return """❌ **Invalid Mobile Number Format**

Please provide a valid mobile number:
• 10-15 digits
• Can include country code (+1, +91, etc.)
• Examples: "+1234567890" or "9876543210"

**Please enter your mobile number:**"""
    
    # Step 3: Collect complaint details
    elif not context.complaint_details:
        if context.current_step == "collecting_details":
            if len(user_message.strip()) < 10:
                return """❌ **Insufficient Details**

Please provide more detailed information about your complaint:
• Minimum 10 characters required
• Be specific about the issue
• Include relevant details like error messages, when it started, etc.

**Please describe your complaint in detail:**"""

            context.complaint_details = user_message.strip()
            context.current_step = "processing"
            
            # Register complaint via API
            # Use default category if LLM handler is not available
            if st.session_state.llm_handler:
                try:
                    category = st.session_state.llm_handler.categorize_complaint(context.complaint_details)
                    category_value = category.value
                except:
                    category_value = "Other"
            else:
                category_value = "Other"

            result = register_complaint_api(
                context.name,
                context.mobile,
                context.complaint_details,
                category_value
            )
            
            if result["success"]:
                complaint_data = result["data"]
                complaint_id = complaint_data["complaint_id"]

                # Reset context for next interaction
                st.session_state.user_context = UserContext()

                return f"""🎉 **Complaint Registered Successfully!**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**📋 COMPLAINT DETAILS**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**🆔 Complaint ID:** `{complaint_id}`
**👤 Name:** {context.name}
**📱 Mobile:** {context.mobile}
**📊 Status:** {complaint_data['status']}
**🏷️ Category:** {complaint_data['category']}
**📅 Registered:** {complaint_data['created_at'][:10]}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📌 IMPORTANT NOTES:**
• **Save your Complaint ID:** `{complaint_id}`
• **Expected Resolution:** 2-5 business days
• **Status Updates:** Check anytime with your Complaint ID
• **Reference Number:** Keep this for all future communications

**🔍 To Check Status:** Simply say "Check status" and provide your Complaint ID

Thank you for using our Grievance Management System. We'll resolve your issue promptly!

Is there anything else I can help you with?"""
            else:
                # Reset context on error too
                st.session_state.user_context = UserContext()
                return f"""❌ **Registration Failed**

We encountered an error while registering your complaint:
**Error:** {result['error']}

**Please try again by saying:** "I want to register a complaint"

If the problem persists, please contact our support team."""
    
    return "I'm processing your request. Please wait..."

def handle_status_check(user_message: str, context: UserContext, extracted_info: Dict) -> str:
    """Handle status check requests"""
    
    # Look for complaint ID in the message
    complaint_id_pattern = r"CMP[A-Z0-9]{8}"
    complaint_id_match = re.search(complaint_id_pattern, user_message.upper())
    
    if complaint_id_match:
        complaint_id = complaint_id_match.group()
        result = get_complaint_status_api(complaint_id)
        
        if result["success"]:
            complaint = result["data"]

            # Status-specific messages
            status_messages = {
                "Registered": "📝 Your complaint has been received and is in our system",
                "In Progress": "⚙️ Our team is actively working on your complaint",
                "Under Review": "🔍 Your complaint is being reviewed by our specialists",
                "Resolved": "✅ Your complaint has been resolved successfully",
                "Closed": "📁 Your complaint has been closed",
                "Rejected": "❌ Your complaint could not be processed"
            }

            status_msg = status_messages.get(complaint['status'], "📊 Status updated")

            return f"""📋 **COMPLAINT STATUS REPORT**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**🆔 Complaint ID:** `{complaint['complaint_id']}`
**📊 Current Status:** **{complaint['status']}**
**🏷️ Category:** {complaint['category']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📅 Timeline:**
• **Registered:** {complaint['created_at'][:10]}
• **Last Updated:** {complaint['updated_at'][:10]}

**📝 Complaint Details:**
{complaint['complaint_details'][:150]}{"..." if len(complaint['complaint_details']) > 150 else ""}

**ℹ️ Status Information:**
{status_msg}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Need help with anything else? You can:
• Register a new complaint
• Check another complaint status"""
        else:
            return f"""❌ **Complaint Not Found**

**Error:** {result['error']}

**Please verify:**
• Complaint ID format: CMP12345678
• Check for typos in the ID
• Ensure the complaint was registered in this system

**Need help?** Say "help" for assistance or try registering a new complaint."""
    
    # Look for mobile number
    elif "mobile" in extracted_info or validate_mobile(user_message.strip()):
        mobile = extracted_info.get("mobile", user_message.strip())
        result = get_user_complaints_api(mobile)
        
        if result["success"]:
            complaints = result["data"]
            if complaints:
                response = f"""📱 **COMPLAINTS SUMMARY**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**📞 Mobile Number:** {mobile}
**📊 Total Complaints:** {len(complaints)}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**📋 Recent Complaints:**
"""
                for i, complaint in enumerate(complaints[:5], 1):  # Show latest 5
                    status_emoji = {
                        "Registered": "📝",
                        "In Progress": "⚙️",
                        "Under Review": "🔍",
                        "Resolved": "✅",
                        "Closed": "📁",
                        "Rejected": "❌"
                    }.get(complaint['status'], "📊")

                    response += f"""
**{i}.** `{complaint['complaint_id']}` {status_emoji}
   **Status:** {complaint['status']}
   **Date:** {complaint['created_at'][:10]}
   **Category:** {complaint['category']}"""

                response += f"""

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**🔍 For detailed status:** Provide a specific Complaint ID
**📝 To register new complaint:** Say "I have a complaint"

Which complaint would you like to check in detail?"""
                return response
            else:
                return f"""📱 **No Complaints Found**

**Mobile Number:** {mobile}

**This could mean:**
• No complaints registered with this number
• Different mobile number was used during registration
• Typo in the mobile number

**Would you like to:**
• Register a new complaint?
• Try a different mobile number?
• Check with a Complaint ID instead?"""
        else:
            return f"""❌ **Error Fetching Complaints**

**Error:** {result['error']}

**Please try:**
• Check your mobile number format
• Ensure you're using the registered number
• Contact support if the issue persists"""
    
    else:
        return """🔍 **Status Check Required**

To check your complaint status, please provide **one of the following:**

**Option 1: Complaint ID**
• Format: `CMP12345678`
• Example: "Check status CMP9B41CA0F"
• Most accurate method

**Option 2: Mobile Number**
• Use the number you registered with
• Example: "+1234567890" or "9876543210"
• Shows all your complaints

**Sample Commands:**
• "Status of CMP9B41CA0F"
• "Check my complaints for +1234567890"
• "What's the status of my complaint CMP12345678"

Please provide your Complaint ID or mobile number to proceed."""

def get_general_response(user_message: str, context: UserContext) -> str:
    """Generate general responses for greetings and help"""
    message_lower = user_message.lower()

    if any(word in message_lower for word in ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]):
        return """👋 Hello! Welcome to the Grievance Management System.

I'm here to help you with:
• **Register a complaint** - Say "I have a complaint" or "I want to file a complaint"
• **Check complaint status** - Provide your Complaint ID or mobile number

How can I assist you today?"""

    elif any(word in message_lower for word in ["help", "what can you do", "how does this work"]):
        return """🆘 **How I can help you:**

**To Register a Complaint:**
1. Say "I have a complaint" or "I want to register a complaint"
2. I'll ask for your name, mobile number, and complaint details
3. You'll receive a unique Complaint ID for tracking

**To Check Status:**
1. Say "Check my complaint status"
2. Provide your Complaint ID (format: CMP12345678) or mobile number
3. I'll show you the current status and details

**Sample Complaint ID:** CMP9B41CA0F

What would you like to do?"""

    elif any(word in message_lower for word in ["thank", "thanks", "bye", "goodbye"]):
        return """🙏 Thank you for using our Grievance Management System!

If you need any further assistance, feel free to ask. Have a great day!"""

    else:
        return """🤖 I'm the Grievance Management Assistant. I can help you:

• **Register a new complaint** - Just say "I have a complaint"
• **Check complaint status** - Provide your Complaint ID or mobile number

Please let me know what you'd like to do!"""

# Main UI
def main():
    # Header
    st.title("🎧 Professional Grievance Management System")
    st.markdown("### *AI-Powered Complaint Registration & Status Tracking*")
    st.markdown("---")

    # Sidebar
    with st.sidebar:
        st.header("📊 System Status")

        # API Status
        api_status = check_api_server()
        if api_status:
            st.success("🟢 **API Server:** Online")
        else:
            st.error("🔴 **API Server:** Offline")
            st.warning("⚠️ Please start the API server:\n```bash\npython api_server.py\n```")

        st.markdown("---")
        st.header("📋 Quick Guide")

        with st.expander("🆕 Register New Complaint"):
            st.markdown("""
            **Steps:**
            1. Say "I have a complaint"
            2. Provide your full name
            3. Enter mobile number
            4. Describe the issue
            5. Get your Complaint ID
            """)

        with st.expander("🔍 Check Complaint Status"):
            st.markdown("""
            **Methods:**
            • **By ID:** Provide Complaint ID (CMP12345678)
            • **By Mobile:** Enter registered mobile number

            **Sample Commands:**
            • "Check status CMP9B41CA0F"
            • "Status for +1234567890"
            """)

        st.markdown("---")
        st.header("🛠️ Actions")

        if st.button("🔄 Clear Chat History", use_container_width=True):
            st.session_state.messages = []
            st.session_state.user_context = UserContext()
            st.rerun()

        if st.button("📊 View Sample Data", use_container_width=True):
            st.info("""
            **Sample Complaint IDs to test:**
            • CMP9B41CA0F
            • CMPBD678C56
            • CMP3A066B16
            """)

        st.markdown("---")
        st.markdown("**💡 Tips:**")
        st.markdown("• Keep your Complaint ID safe")
        st.markdown("• Check status regularly")
        st.markdown("• Provide detailed descriptions")
    
    # Welcome message for new users
    if not st.session_state.messages:
        with st.chat_message("assistant"):
            st.markdown("""👋 **Welcome to the Professional Grievance Management System!**

I'm your AI assistant, ready to help you with:

🆕 **Register New Complaints**
🔍 **Check Complaint Status**
📊 **Track Your Issues**

**To get started, try saying:**
• "I have a complaint to register"
• "Check my complaint status"
• "Help me with my issue"

How can I assist you today?""")

    # Chat interface
    chat_container = st.container()

    with chat_container:
        # Display chat messages
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])

        # Chat input with better placeholder
        placeholder_text = "Type your message here... (e.g., 'I have a complaint' or 'Check status CMP12345678')"

        if prompt := st.chat_input(placeholder_text):
            # Add user message
            st.session_state.messages.append({"role": "user", "content": prompt})

            with st.chat_message("user"):
                st.markdown(prompt)

            # Generate and display assistant response
            with st.chat_message("assistant"):
                with st.spinner("🤖 Processing your request..."):
                    if not api_status:
                        response = """❌ **System Unavailable**

The API server is currently offline. Please ensure the server is running:

```bash
python api_server.py
```

Contact your system administrator if the problem persists."""
                    else:
                        response = process_user_message(prompt)

                st.markdown(response)
                st.session_state.messages.append({"role": "assistant", "content": response})

    # Footer
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #666; font-size: 0.8em;'>"
        "🎧 Professional Grievance Management System | "
        "Powered by AI & RAG Technology | "
        "© 2024 Cyfuture Assignment"
        "</div>",
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
