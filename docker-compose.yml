version: '3.8'

services:
  grievance-system:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: grievance-management-system
    ports:
      - "8000:8000"  # API Server
      - "8501:8501"  # Streamlit App
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - GROQ_API_KEY=${GROQ_API_KEY:-}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - grievance-network

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: grievance-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - grievance-system
    restart: unless-stopped
    networks:
      - grievance-network
    profiles:
      - production

networks:
  grievance-network:
    driver: bridge

volumes:
  grievance-data:
    driver: local
  grievance-logs:
    driver: local
