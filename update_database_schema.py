#!/usr/bin/env python3
"""
Update database schema to add email column
"""

import sqlite3
import os
from pathlib import Path

def update_database_schema():
    """Add email column to existing database"""
    db_path = Path("data/grievance_system.db")
    
    if not db_path.exists():
        print("Database doesn't exist yet, will be created with new schema")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if email column already exists
        cursor.execute("PRAGMA table_info(complaints)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'email' not in columns:
            print("Adding email column to complaints table...")
            cursor.execute("ALTER TABLE complaints ADD COLUMN email TEXT")
            conn.commit()
            print("✅ Email column added successfully")
        else:
            print("✅ Email column already exists")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating database schema: {e}")
        return False

if __name__ == "__main__":
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    success = update_database_schema()
    if success:
        print("🎉 Database schema update completed!")
    else:
        print("❌ Database schema update failed!")
