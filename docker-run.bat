@echo off
REM Grievance Management System - Docker Runner for Windows

echo 🐳 Grievance Management System - Docker Setup
echo ==============================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

echo ✅ Docker is available

REM Stop and remove existing container if it exists
echo ℹ️  Checking for existing container...
docker stop grievance-management-system >nul 2>&1
docker rm grievance-management-system >nul 2>&1

REM Build the Docker image
echo ℹ️  Building Docker image...
docker build -t grievance-management-system .
if errorlevel 1 (
    echo ❌ Failed to build Docker image
    pause
    exit /b 1
)

echo ✅ Docker image built successfully

REM Run the container
echo ℹ️  Starting the container...
docker run -d --name grievance-management-system -p 8000:8000 -p 8501:8501 -v "%cd%/data:/app/data" --restart unless-stopped grievance-management-system

if errorlevel 1 (
    echo ❌ Failed to start container
    pause
    exit /b 1
)

echo ✅ Container started successfully

REM Wait for services to start
echo ℹ️  Waiting for services to start...
timeout /t 10 /nobreak >nul

echo.
echo 🎉 GRIEVANCE MANAGEMENT SYSTEM STARTED!
echo ========================================
echo.
echo 🌐 Access URLs:
echo    • Main Application: http://localhost:8501
echo    • Admin Panel: http://localhost:8501/admin
echo    • API Documentation: http://localhost:8000/docs
echo.
echo 👨‍💼 Admin Credentials:
echo    • Username: admin
echo    • Password: admin123
echo.
echo 🔧 Docker Commands:
echo    • View logs: docker logs grievance-management-system
echo    • Stop system: docker stop grievance-management-system
echo    • Start system: docker start grievance-management-system
echo    • Remove system: docker rm grievance-management-system
echo.
echo ✅ System is ready for use!
echo.
pause
