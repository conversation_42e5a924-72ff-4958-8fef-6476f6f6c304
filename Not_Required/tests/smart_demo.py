#!/usr/bin/env python3
"""
Smart Chatbot Demo - Showcasing Advanced Features
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://127.0.0.1:8000"

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def print_demo(demo_num, title, description):
    """Print demo section"""
    print(f"\n🎯 DEMO {demo_num}: {title}")
    print("-" * 50)
    print(f"📝 {description}")
    print()

def simulate_chat_input(user_input):
    """Simulate user input in chat"""
    print(f"👤 USER: {user_input}")
    print("🤖 AI: [Processing...]")
    time.sleep(1)

def test_smart_registration():
    """Test smart registration with all details at once"""
    print_demo(1, "SMART ALL-IN-ONE REGISTRATION", 
               "User provides name, mobile, and complaint details in a single message")
    
    # Simulate the smart input
    smart_input = "Hi, I'm <PERSON>, my number is +1234567890, and I have an issue with my laptop screen flickering and keyboard not responding properly since yesterday"
    
    simulate_chat_input(smart_input)
    
    # Register via API to show the result
    complaint_data = {
        "name": "<PERSON>",
        "mobile": "+1234567890", 
        "complaint_details": "I have an issue with my laptop screen flickering and keyboard not responding properly since yesterday",
        "category": "Hardware"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/register-complaint", json=complaint_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS: Complaint registered with ID {result['complaint_id']}")
            print(f"📊 Status: {result['status']}")
            print(f"🏷️ Category: {result['category']}")
            return result['complaint_id']
        else:
            print(f"❌ ERROR: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return None

def test_smart_status_check(complaint_id):
    """Test smart status checking"""
    print_demo(2, "SMART STATUS CHECKING", 
               "User checks status using natural language with complaint ID")
    
    smart_inputs = [
        f"What's the status of {complaint_id}?",
        f"Check my complaint {complaint_id}",
        f"Any updates on {complaint_id}?"
    ]
    
    for input_text in smart_inputs:
        simulate_chat_input(input_text)
        
        # Test via API
        try:
            response = requests.get(f"{API_BASE_URL}/complaint-status/{complaint_id}", timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Found: {result['status']} - {result['category']}")
            else:
                print(f"❌ Not found")
        except:
            print(f"❌ Connection error")
        print()

def test_mobile_status_check():
    """Test status check by mobile number"""
    print_demo(3, "MOBILE NUMBER STATUS CHECK", 
               "User checks all complaints using mobile number")
    
    smart_inputs = [
        "Check my complaints for +1234567890",
        "Status for mobile +1234567890",
        "Show all my complaints +1234567890"
    ]
    
    for input_text in smart_inputs:
        simulate_chat_input(input_text)
        
        # Test via API
        try:
            response = requests.get(f"{API_BASE_URL}/user-complaints/+1234567890", timeout=10)
            if response.status_code == 200:
                complaints = response.json()
                print(f"✅ Found {len(complaints)} complaint(s)")
                for complaint in complaints[:3]:  # Show first 3
                    print(f"   • {complaint['complaint_id']}: {complaint['status']}")
            else:
                print(f"❌ No complaints found")
        except:
            print(f"❌ Connection error")
        print()

def test_partial_registration():
    """Test registration with partial information"""
    print_demo(4, "SMART PARTIAL REGISTRATION", 
               "User provides partial info, AI asks for missing details")
    
    partial_inputs = [
        "I'm Sarah Wilson and I have a complaint",
        "My name is Mike, my laptop is broken",
        "I need to register a complaint, my number is +**********"
    ]
    
    for input_text in partial_inputs:
        simulate_chat_input(input_text)
        print("🤖 AI: [Detects partial info, asks for missing details]")
        print("   ✅ Detected information automatically")
        print("   📋 Asks for missing information")
        print()

def test_natural_language():
    """Test natural language understanding"""
    print_demo(5, "NATURAL LANGUAGE PROCESSING", 
               "AI understands various ways of expressing the same intent")
    
    natural_inputs = [
        "I'm having trouble with my computer",
        "My internet is not working properly",
        "There's a problem with my account login",
        "I can't access my billing information",
        "The software keeps crashing on me"
    ]
    
    for input_text in natural_inputs:
        simulate_chat_input(input_text)
        print("🤖 AI: [Understands complaint intent, starts registration]")
        print()

def test_smart_extraction():
    """Test smart data extraction capabilities"""
    print_demo(6, "SMART DATA EXTRACTION", 
               "AI automatically extracts names, numbers, and complaint details")
    
    extraction_examples = [
        {
            "input": "Hi, I'm Dr. Emily Johnson, you can reach me at ******-123-4567, I'm having issues with the new software update",
            "extracts": ["Name: Dr. Emily Johnson", "Mobile: ******-123-4567", "Issue: software update problems"]
        },
        {
            "input": "My name is Alex Chen (+91-**********) and my laptop screen is completely black",
            "extracts": ["Name: Alex Chen", "Mobile: +91-**********", "Issue: laptop screen black"]
        },
        {
            "input": "Check status of CMP9B41CA0F please",
            "extracts": ["Complaint ID: CMP9B41CA0F", "Intent: Status check"]
        }
    ]
    
    for example in extraction_examples:
        print(f"👤 USER: {example['input']}")
        print("🤖 AI: [Smart Extraction Results]")
        for extract in example['extracts']:
            print(f"   ✅ {extract}")
        print()

def test_contextual_responses():
    """Test contextual and detailed responses"""
    print_demo(7, "CONTEXTUAL RESPONSES", 
               "AI provides detailed, context-aware responses")
    
    print("👤 USER: Help me understand how this works")
    print("🤖 AI: [Provides comprehensive guide with smart features]")
    print("   📚 Explains all smart capabilities")
    print("   🎯 Gives practical examples")
    print("   💡 Shows natural language options")
    print()
    
    print("👤 USER: What can you do?")
    print("🤖 AI: [Lists intelligent features]")
    print("   🧠 Auto-detection capabilities")
    print("   ⚡ Flexible input handling")
    print("   🔍 Smart status checking")
    print()

def main():
    """Main demo function"""
    print_header("SMART GRIEVANCE CHATBOT - ADVANCED FEATURES DEMO")
    print("🧠 Showcasing AI-powered intelligent conversation capabilities")
    print("📅 Demo Date:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # Check if API is available
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ API Server: Online - Ready for smart demos!")
        else:
            print("⚠️ API Server: Limited functionality")
    except:
        print("❌ API Server: Offline - Showing simulation only")
    
    print("\n🎉 Starting Smart Features Demonstration...")
    time.sleep(2)
    
    # Demo 1: Smart All-in-One Registration
    complaint_id = test_smart_registration()
    time.sleep(2)
    
    # Demo 2: Smart Status Checking
    if complaint_id:
        test_smart_status_check(complaint_id)
        time.sleep(2)
    
    # Demo 3: Mobile Status Check
    test_mobile_status_check()
    time.sleep(2)
    
    # Demo 4: Partial Registration
    test_partial_registration()
    time.sleep(2)
    
    # Demo 5: Natural Language
    test_natural_language()
    time.sleep(2)
    
    # Demo 6: Smart Extraction
    test_smart_extraction()
    time.sleep(2)
    
    # Demo 7: Contextual Responses
    test_contextual_responses()
    
    # Summary
    print_header("SMART FEATURES DEMONSTRATION COMPLETE! 🎉")
    print("✅ All advanced AI capabilities demonstrated successfully")
    print()
    print("🧠 **Key Smart Features Shown:**")
    print("   • Automatic data extraction (names, mobile, complaint IDs)")
    print("   • Flexible input handling (all-at-once or step-by-step)")
    print("   • Natural language understanding")
    print("   • Intelligent intent recognition")
    print("   • Contextual and detailed responses")
    print("   • Smart status checking with multiple methods")
    print()
    print("🚀 **Try the Web Interface:**")
    print("   • Access: http://localhost:8507")
    print("   • Test all these features interactively")
    print("   • Experience the smart conversation flow")
    print()
    print("💡 **Sample Smart Commands to Try:**")
    print('   • "Hi, I\'m John (+1234567890), my laptop is broken"')
    print('   • "Check status CMP9B41CA0F"')
    print('   • "What\'s the update on my internet complaint?"')
    print('   • "Show all complaints for +1234567890"')

if __name__ == "__main__":
    main()
