#!/usr/bin/env python3
"""
Test the conversation flow to ensure it works properly
"""

import sys
import os
sys.path.append('src')

from models.models import UserContext
from frontend.app import process_user_message, extract_basic_info, detect_simple_intent

def test_conversation_flow():
    """Test the complete conversation flow"""
    print("🧪 Testing Conversation Flow")
    print("=" * 50)
    
    # Simulate session state
    class MockSessionState:
        def __init__(self):
            self.user_context = UserContext()
            self.llm_handler = None
    
    # Mock streamlit session state
    import streamlit as st
    if not hasattr(st, 'session_state'):
        st.session_state = MockSessionState()
    else:
        st.session_state.user_context = UserContext()
        st.session_state.llm_handler = None
    
    # Test 1: Basic info extraction
    print("\n1. Testing Information Extraction:")
    test_message = "Hi, I'm <PERSON> and my number is 7709353232"
    extracted = extract_basic_info(test_message)
    print(f"   Input: {test_message}")
    print(f"   Extracted: {extracted}")
    
    # Test 2: Intent detection
    print("\n2. Testing Intent Detection:")
    test_messages = [
        "I have a complaint",
        "Check my status",
        "7709353232",
        "Hello"
    ]
    
    for msg in test_messages:
        intent = detect_simple_intent(msg, st.session_state.user_context)
        print(f"   '{msg}' -> {intent}")
    
    # Test 3: Complete conversation flow
    print("\n3. Testing Complete Conversation:")
    
    # Start complaint registration
    print("\n   User: 'I have a complaint'")
    response1 = process_user_message("I have a complaint")
    print(f"   Bot: {response1[:100]}...")
    
    # Provide name
    print("\n   User: 'John Doe'")
    response2 = process_user_message("John Doe")
    print(f"   Bot: {response2[:100]}...")
    
    # Provide mobile
    print("\n   User: '7709353232'")
    response3 = process_user_message("7709353232")
    print(f"   Bot: {response3[:100]}...")
    
    # Provide complaint details
    print("\n   User: 'My laptop is not working properly'")
    response4 = process_user_message("My laptop is not working properly")
    print(f"   Bot: {response4[:100]}...")
    
    print("\n✅ Conversation flow test completed!")
    
    # Test 4: Status check
    print("\n4. Testing Status Check:")
    st.session_state.user_context = UserContext()  # Reset context
    
    print("\n   User: 'Check status'")
    response5 = process_user_message("Check status")
    print(f"   Bot: {response5[:100]}...")
    
    print("\n   User: '7709353232'")
    response6 = process_user_message("7709353232")
    print(f"   Bot: {response6[:100]}...")
    
    print("\n✅ Status check test completed!")

if __name__ == "__main__":
    test_conversation_flow()
