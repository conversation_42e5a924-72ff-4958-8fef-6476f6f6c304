#!/usr/bin/env python3
"""
Setup script to clear database and add sample records
"""

import sys
import os
sys.path.append('src')

from database.database import DatabaseManager
from models.models import ComplaintCategory

def setup_sample_data():
    """Clear database and add 5 sample records"""
    print("🔄 Setting up sample data...")
    print("=" * 50)
    
    # Initialize database manager
    db = DatabaseManager()
    
    # Clear existing data
    print("1. Clearing existing data...")
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM status_history")
            cursor.execute("DELETE FROM complaints")
            conn.commit()
        print("   ✅ Database cleared")
    except Exception as e:
        print(f"   ⚠️  Error clearing database: {e}")
    
    # Sample complaints data
    sample_complaints = [
        {
            "name": "<PERSON>",
            "mobile": "9876543210",
            "complaint_details": "My laptop is not working properly. The screen keeps flickering and sometimes goes completely black. This is affecting my work productivity.",
            "category": "Hardware"
        },
        {
            "name": "<PERSON>",
            "mobile": "8765432109",
            "complaint_details": "Unable to login to the company portal. Getting 'Invalid credentials' error even with correct username and password. Need urgent access for project submission.",
            "category": "Software"
        },
        {
            "name": "Mike <PERSON>",
            "mobile": "7654321098",
            "complaint_details": "Internet connection is very slow in our office area. Download speed is less than 1 Mbps which is hampering our daily work. Please check the network infrastructure.",
            "category": "Network"
        },
        {
            "name": "Emily Davis",
            "mobile": "6543210987",
            "complaint_details": "Air conditioning system in conference room B is not working. The room temperature is too high for meetings. Please arrange for immediate repair.",
            "category": "Other"
        },
        {
            "name": "Robert Wilson",
            "mobile": "5432109876",
            "complaint_details": "Printer in the 3rd floor is jamming frequently and print quality is very poor. Multiple important documents got damaged. Need replacement or repair.",
            "category": "Hardware"
        }
    ]
    
    # Add sample complaints
    print("2. Adding sample complaints...")
    for i, complaint in enumerate(sample_complaints, 1):
        try:
            from models.models import ComplaintRequest, ComplaintCategory

            # Create ComplaintRequest object
            complaint_req = ComplaintRequest(
                name=complaint["name"],
                mobile=complaint["mobile"],
                complaint_details=complaint["complaint_details"],
                category=ComplaintCategory(complaint["category"])
            )

            # Register complaint
            response = db.register_complaint(complaint_req)
            print(f"   ✅ {i}. {complaint['name']} - {response.complaint_id}")
        except Exception as e:
            print(f"   ❌ Error adding complaint {i}: {e}")
    
    # Verify data
    print("\n3. Verifying sample data...")
    try:
        complaints = db.get_all_complaints()
        print(f"   ✅ Total complaints in database: {len(complaints)}")
        
        print("\n📋 Sample Complaints Added:")
        print("-" * 80)
        for complaint in complaints:
            print(f"🆔 {complaint.complaint_id} | 👤 {complaint.name} | 📱 {complaint.mobile}")
            print(f"📊 Status: {complaint.status.value} | 🏷️ Category: {complaint.category.value}")
            print(f"📝 Details: {complaint.complaint_details[:60]}...")
            print("-" * 80)
            
    except Exception as e:
        print(f"   ❌ Error verifying data: {e}")
    
    print("\n🎉 Sample data setup completed!")
    print("✅ Database is ready with 5 sample complaints")
    print("🚀 You can now start the system and test all functionality")

if __name__ == "__main__":
    setup_sample_data()
