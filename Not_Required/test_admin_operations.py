#!/usr/bin/env python3
"""
Test admin operations to ensure CRUD functionality works
"""

import requests
import json

API_BASE_URL = "http://127.0.0.1:8000"

def test_admin_operations():
    """Test all admin CRUD operations"""
    print("🧪 Testing Admin CRUD Operations")
    print("=" * 50)
    
    # Test 1: Get all complaints
    print("\n1. Testing GET all complaints:")
    try:
        response = requests.get(f"{API_BASE_URL}/admin/complaints")
        if response.status_code == 200:
            complaints = response.json()
            print(f"   ✅ Found {len(complaints)} complaints")
            if complaints:
                test_complaint_id = complaints[0]['complaint_id']
                print(f"   📋 Using complaint ID: {test_complaint_id}")
            else:
                print("   ⚠️  No complaints found for testing")
                return
        else:
            print(f"   ❌ Failed: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Test 2: Update complaint status
    print(f"\n2. Testing UPDATE status for {test_complaint_id}:")
    try:
        response = requests.put(
            f"{API_BASE_URL}/admin/complaint/{test_complaint_id}/status",
            json={"status": "In Progress"}
        )
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Status updated: {result}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Get specific complaint to verify update
    print(f"\n3. Testing GET specific complaint {test_complaint_id}:")
    try:
        response = requests.get(f"{API_BASE_URL}/complaint-status/{test_complaint_id}")
        if response.status_code == 200:
            complaint = response.json()
            print(f"   ✅ Status verified: {complaint['status']}")
            print(f"   📅 Updated at: {complaint['updated_at']}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Create a test complaint for deletion
    print("\n4. Testing CREATE complaint for deletion test:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/register-complaint",
            json={
                "name": "Test Delete User",
                "mobile": "1000000000",
                "complaint_details": "Test complaint for deletion - this is a test complaint created for testing delete functionality"
            }
        )
        if response.status_code == 200:
            new_complaint = response.json()
            delete_test_id = new_complaint['complaint_id']
            print(f"   ✅ Created test complaint: {delete_test_id}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Test 5: Delete the test complaint
    print(f"\n5. Testing DELETE complaint {delete_test_id}:")
    try:
        response = requests.delete(f"{API_BASE_URL}/admin/complaint/{delete_test_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Deleted successfully: {result}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 6: Verify deletion
    print(f"\n6. Testing GET deleted complaint {delete_test_id} (should fail):")
    try:
        response = requests.get(f"{API_BASE_URL}/complaint-status/{delete_test_id}")
        if response.status_code == 404:
            print(f"   ✅ Correctly returns 404 - complaint deleted")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 7: Test error handling - non-existent complaint
    print(f"\n7. Testing error handling with non-existent complaint:")
    fake_id = "CMP00000000"
    try:
        response = requests.put(
            f"{API_BASE_URL}/admin/complaint/{fake_id}/status",
            json={"status": "Resolved"}
        )
        if response.status_code == 404:
            print(f"   ✅ Correctly returns 404 for non-existent complaint")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 8: Get admin statistics
    print(f"\n8. Testing GET admin statistics:")
    try:
        response = requests.get(f"{API_BASE_URL}/admin/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Statistics retrieved:")
            print(f"      Total complaints: {stats.get('total_complaints', 'N/A')}")
            print(f"      By status: {stats.get('by_status', 'N/A')}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Admin CRUD Operations Test Completed!")
    print("✅ All basic CRUD operations are working correctly")
    print("✅ Error handling is functioning properly")
    print("✅ API endpoints are responding as expected")

if __name__ == "__main__":
    test_admin_operations()
