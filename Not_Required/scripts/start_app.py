#!/usr/bin/env python3
"""
Startup script for Grievance Management System
This script helps start both the API server and Streamlit app
"""

import subprocess
import sys
import time
import os
import signal
from threading import Thread
import requests

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import streamlit
        import fastapi
        import groq
        import sentence_transformers
        print("✅ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install dependencies with: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists and has required variables"""
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("Please copy .env.example to .env and add your Groq API key")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
        if 'GROQ_API_KEY=' not in content or 'your_groq_api_key_here' in content:
            print("❌ Please set your GROQ_API_KEY in the .env file")
            return False
    
    print("✅ Environment configuration looks good")
    return True

def initialize_database():
    """Initialize the database"""
    try:
        print("🔄 Initializing database...")
        result = subprocess.run([sys.executable, 'init_db.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Database initialized successfully")
            return True
        else:
            print(f"❌ Database initialization failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        return False

def start_api_server():
    """Start the FastAPI server"""
    try:
        print("🚀 Starting API server...")
        process = subprocess.Popen([
            sys.executable, 'api_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a bit for server to start
        time.sleep(3)
        
        # Check if server is running
        try:
            response = requests.get('http://127.0.0.1:8000/', timeout=5)
            if response.status_code == 200:
                print("✅ API server started successfully")
                return process
        except:
            pass
        
        print("❌ API server failed to start")
        return None
        
    except Exception as e:
        print(f"❌ Error starting API server: {e}")
        return None

def start_streamlit_app():
    """Start the Streamlit app"""
    try:
        print("🚀 Starting Streamlit app...")
        process = subprocess.Popen([
            sys.executable, '-m', 'streamlit', 'run', 'app.py'
        ])
        return process
    except Exception as e:
        print(f"❌ Error starting Streamlit app: {e}")
        return None

def main():
    """Main startup function"""
    print("🎧 Grievance Management System Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Check environment
    if not check_env_file():
        return 1
    
    # Initialize database
    if not initialize_database():
        return 1
    
    print("\n🚀 Starting services...")
    
    # Start API server
    api_process = start_api_server()
    if not api_process:
        return 1
    
    # Start Streamlit app
    streamlit_process = start_streamlit_app()
    if not streamlit_process:
        api_process.terminate()
        return 1
    
    print("\n✅ All services started successfully!")
    print("📱 Streamlit app should open in your browser automatically")
    print("🌐 API server running at: http://127.0.0.1:8000")
    print("🎯 Streamlit app running at: http://localhost:8501")
    print("\n⚠️  Press Ctrl+C to stop all services")
    
    try:
        # Wait for processes
        while True:
            time.sleep(1)
            
            # Check if processes are still running
            if api_process.poll() is not None:
                print("❌ API server stopped unexpectedly")
                break
            
            if streamlit_process.poll() is not None:
                print("❌ Streamlit app stopped unexpectedly")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Shutting down services...")
        
        # Terminate processes
        try:
            api_process.terminate()
            streamlit_process.terminate()
            
            # Wait for graceful shutdown
            api_process.wait(timeout=5)
            streamlit_process.wait(timeout=5)
            
        except subprocess.TimeoutExpired:
            # Force kill if needed
            api_process.kill()
            streamlit_process.kill()
        
        print("✅ All services stopped")
    
    return 0

if __name__ == "__main__":
    exit(main())
