#!/usr/bin/env python3
"""
Docker-specific startup script for Grievance Management System
Optimized for container environments
"""

import subprocess
import time
import sys
import os
import signal
from threading import Thread

def initialize_database():
    """Initialize the database"""
    print("💾 Initializing Database...")
    try:
        # Set up Python path for Docker
        sys.path.insert(0, '/app')
        sys.path.insert(0, '/app/src')
        os.chdir('/app')

        from src.database.database import DatabaseManager

        db = DatabaseManager()
        db.init_database()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_api_server():
    """Start the FastAPI server for Docker"""
    print("🚀 Starting API Server...")

    # Set up environment for API server
    env = os.environ.copy()
    env['PYTHONPATH'] = '/app:/app/src'
    env['HOST'] = '0.0.0.0'

    # Start API server with proper working directory
    api_process = subprocess.Popen([
        sys.executable, "src/api/api_server.py"
    ], cwd='/app', env=env)

    return api_process

def start_streamlit_app():
    """Start the Streamlit app for Docker"""
    print("🎨 Starting Streamlit Frontend...")

    # Set up environment for Streamlit
    env = os.environ.copy()
    env['PYTHONPATH'] = '/app:/app/src'

    # Start Streamlit with Docker-friendly settings
    streamlit_process = subprocess.Popen([
        sys.executable, "-m", "streamlit", "run",
        "src/frontend/app.py",
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false",
        "--server.enableCORS", "false",
        "--server.enableXsrfProtection", "false"
    ], cwd='/app', env=env)

    return streamlit_process

def main():
    """Main startup function for Docker"""
    print("=" * 80)
    print("🎯 GRIEVANCE MANAGEMENT SYSTEM (Docker)")
    print("🚀 Professional AI-Powered Complaint Management")
    print("=" * 80)
    
    # Initialize database
    if not initialize_database():
        print("❌ Cannot start system without database")
        sys.exit(1)
    
    processes = []
    
    try:
        # Start API server
        api_process = start_api_server()
        if not api_process:
            print("❌ Failed to start API server")
            sys.exit(1)
        processes.append(api_process)
        
        # Wait a bit for API to start
        time.sleep(5)
        
        # Start Streamlit app
        streamlit_process = start_streamlit_app()
        if not streamlit_process:
            print("❌ Failed to start Streamlit frontend")
            sys.exit(1)
        processes.append(streamlit_process)
        
        # Wait for services to be ready
        time.sleep(10)
        
        print("\n" + "=" * 80)
        print("🎉 SYSTEM STARTED SUCCESSFULLY!")
        print("=" * 80)
        print("🌐 Frontend URL: http://localhost:8501")
        print("🔧 API Documentation: http://localhost:8000/docs")
        print("👨‍💼 Admin Panel: http://localhost:8501/admin")
        print("\n📋 Default Admin Credentials:")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🔄 System Features:")
        print("   ✅ AI-Powered Complaint Processing")
        print("   ✅ Mobile Number-Based Search")
        print("   ✅ Real-time Status Updates")
        print("   ✅ Professional Admin Dashboard")
        print("   ✅ Context-Aware Responses")
        print("   ✅ RAG-Enhanced Knowledge Base")
        print("\n⚠️  Container is running - check Docker logs for status")
        print("=" * 80)
        
        # Keep running until interrupted
        try:
            while True:
                time.sleep(1)
                # Check if processes are still running
                for process in processes:
                    if process.poll() is not None:
                        print("⚠️  A service has stopped unexpectedly")
                        break
        except KeyboardInterrupt:
            print("\n🛑 Shutting down services...")
            
    except Exception as e:
        print(f"❌ Error starting system: {e}")
        sys.exit(1)
    
    finally:
        # Clean up processes
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        print("✅ All services stopped")

if __name__ == "__main__":
    main()
