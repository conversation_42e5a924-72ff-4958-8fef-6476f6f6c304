#!/usr/bin/env python3
"""
Database initialization script for Grievance Management System
"""

import sqlite3
from datetime import datetime
import random
from database import DatabaseManager
from models import ComplaintRequest, ComplaintCategory, ComplaintStatus

def create_sample_data():
    """Create sample complaints for testing"""
    db_manager = DatabaseManager()
    
    sample_complaints = [
        {
            "name": "<PERSON>",
            "mobile": "+**********",
            "complaint_details": "My laptop is not starting up. The screen remains black even after pressing the power button multiple times. This started happening yesterday.",
            "category": ComplaintCategory.HARDWARE
        },
        {
            "name": "<PERSON>", 
            "mobile": "+**********",
            "complaint_details": "The accounting software keeps crashing when I try to generate monthly reports. Getting error code 500.",
            "category": ComplaintCategory.SOFTWARE
        },
        {
            "name": "<PERSON>",
            "mobile": "+**********", 
            "complaint_details": "Internet connection is very slow in our office. Pages take forever to load and video calls keep dropping.",
            "category": ComplaintCategory.NETWORK
        },
        {
            "name": "<PERSON>",
            "mobile": "+**********",
            "complaint_details": "Cannot login to my company account. Password reset is not working and I'm locked out.",
            "category": ComplaintCategory.ACCOUNT
        },
        {
            "name": "David Brown",
            "mobile": "+**********",
            "complaint_details": "There are incorrect charges on my monthly bill. I was charged for services I didn't use.",
            "category": ComplaintCategory.BILLING
        }
    ]
    
    print("Creating sample complaints...")
    
    for complaint_data in sample_complaints:
        try:
            complaint = ComplaintRequest(**complaint_data)
            result = db_manager.register_complaint(complaint)
            print(f"✅ Created complaint: {result.complaint_id} - {result.name}")
            
            # Simulate some status updates
            if random.choice([True, False]):
                new_status = random.choice([
                    ComplaintStatus.IN_PROGRESS,
                    ComplaintStatus.UNDER_REVIEW,
                    ComplaintStatus.RESOLVED
                ])
                db_manager.update_complaint_status(
                    result.complaint_id,
                    new_status,
                    f"Status updated for demo purposes"
                )
                print(f"   Updated status to: {new_status.value}")
                
        except Exception as e:
            print(f"❌ Error creating complaint for {complaint_data['name']}: {e}")
    
    print(f"\n✅ Sample data creation completed!")

def main():
    """Main initialization function"""
    print("🚀 Initializing Grievance Management System Database...")
    
    try:
        # Initialize database
        db_manager = DatabaseManager()
        print("✅ Database tables created successfully!")
        
        # Create sample data
        create_sample_data()
        
        # Display summary
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM complaints")
            total_complaints = cursor.fetchone()[0]
            
            cursor.execute("SELECT status, COUNT(*) FROM complaints GROUP BY status")
            status_counts = cursor.fetchall()
        
        print(f"\n📊 Database Summary:")
        print(f"Total Complaints: {total_complaints}")
        print("Status Distribution:")
        for status, count in status_counts:
            print(f"  - {status}: {count}")
        
        print(f"\n🎉 Database initialization completed successfully!")
        print(f"You can now start the application with:")
        print(f"  1. python api_server.py (in one terminal)")
        print(f"  2. streamlit run app.py (in another terminal)")
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
