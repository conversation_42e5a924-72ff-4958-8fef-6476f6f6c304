# 📊 Database Directory

## 💾 Database Files

This directory contains the SQLite database files for the Grievance Management System.

### 📁 Contents:
- `grievance_system.db` - Main application database (auto-generated)

### 🔄 Auto-Generation:
The database file is automatically created when you first run the system:
```bash
python start_system.py
```

### 📋 Database Schema:
- **complaints** - Main complaints table
- **status_history** - Status change tracking
- **users** - User information (if applicable)

### 🧪 Sample Data:
The system automatically creates 5 sample complaints for testing:
1. <PERSON> - Hardware issue
2. <PERSON> - Software issue  
3. <PERSON> - Network issue
4. <PERSON> - Other issue
5. <PERSON> - Hardware issue

### ⚠️ Note:
Database files are excluded from Git repository for security and size reasons. The database will be automatically initialized on first run.
