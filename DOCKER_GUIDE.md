# 🐳 Docker Deployment Guide

## 🚀 Easy Docker Setup for Grievance Management System

This guide provides multiple ways to run the Grievance Management System using Docker for easy sharing and deployment.

---

## 📋 Prerequisites

### **Required:**
- Docker Desktop installed and running
- 4GB RAM minimum
- 2GB free disk space

### **Installation Links:**
- **Windows/Mac**: [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- **Linux**: [Docker Engine](https://docs.docker.com/engine/install/)

---

## 🎯 Quick Start Options

### **Option 1: One-Click Scripts (Recommended)**

#### **For Linux/Mac:**
```bash
./docker-run.sh
```

#### **For Windows:**
```cmd
docker-run.bat
```

### **Option 1.5: Simple Docker Run (Fastest)**
```bash
# Build and run in one go
docker build -t grievance-management-system .
docker run -d --name grievance-management-system \
  -p 8000:8000 -p 8501:8501 \
  -v $(pwd)/data:/app/data \
  grievance-management-system
```

### **Option 2: Docker Compose (Advanced)**
```bash
docker-compose up -d
```

### **Option 3: Manual Docker Commands**
```bash
# Build the image
docker build -t grievance-management-system .

# Run the container
docker run -d \
  --name grievance-management-system \
  -p 8000:8000 \
  -p 8501:8501 \
  -v $(pwd)/data:/app/data \
  grievance-management-system
```

---

## 🌐 Access URLs

Once the Docker container is running:

| Service | URL | Description |
|---------|-----|-------------|
| **Main App** | http://localhost:8501 | User interface for complaints |
| **Admin Panel** | http://localhost:8501/admin | Admin management (admin/admin123) |
| **API Docs** | http://localhost:8000/docs | Interactive API documentation |
| **Health Check** | http://localhost:8000/ | API status endpoint |

---

## 🔧 Docker Management Commands

### **Container Operations:**
```bash
# View running containers
docker ps

# View all containers
docker ps -a

# View container logs
docker logs grievance-management-system

# Follow logs in real-time
docker logs -f grievance-management-system

# Stop the container
docker stop grievance-management-system

# Start the container
docker start grievance-management-system

# Restart the container
docker restart grievance-management-system

# Remove the container
docker rm grievance-management-system
```

### **Image Operations:**
```bash
# List images
docker images

# Remove the image
docker rmi grievance-management-system

# Rebuild image (after code changes)
docker build -t grievance-management-system . --no-cache
```

---

## 📊 Container Details

### **Exposed Ports:**
- **8000**: FastAPI backend server
- **8501**: Streamlit frontend server

### **Volume Mounts:**
- `./data:/app/data` - Database persistence
- `./logs:/app/logs` - Log file persistence

### **Environment Variables:**
- `GROQ_API_KEY` - Optional API key for enhanced AI features
- `DATABASE_PATH` - Database file location
- `LOG_LEVEL` - Logging level (INFO, DEBUG, ERROR)

---

## 🔄 Development Workflow

### **For Code Changes:**
```bash
# Stop the container
docker stop grievance-management-system

# Remove the container
docker rm grievance-management-system

# Rebuild with changes
docker build -t grievance-management-system .

# Run updated container
docker run -d --name grievance-management-system \
  -p 8000:8000 -p 8501:8501 \
  -v $(pwd)/data:/app/data \
  grievance-management-system
```

### **Quick Rebuild Script:**
```bash
# Create a rebuild script
cat > rebuild.sh << 'EOF'
#!/bin/bash
docker stop grievance-management-system
docker rm grievance-management-system
docker build -t grievance-management-system .
docker run -d --name grievance-management-system \
  -p 8000:8000 -p 8501:8501 \
  -v $(pwd)/data:/app/data \
  grievance-management-system
EOF

chmod +x rebuild.sh
```

---

## 📦 Sharing the Application

### **Method 1: Share Source Code**
```bash
# Create a distributable package
tar -czf grievance-system.tar.gz \
  --exclude=Not_Required \
  --exclude=__pycache__ \
  --exclude=*.log \
  .

# Recipients can extract and run:
tar -xzf grievance-system.tar.gz
cd grievance-system
./docker-run.sh
```

### **Method 2: Share Docker Image**
```bash
# Save image to file
docker save grievance-management-system > grievance-system.tar

# Load image on another machine
docker load < grievance-system.tar

# Run the loaded image
docker run -d --name grievance-management-system \
  -p 8000:8000 -p 8501:8501 \
  grievance-management-system
```

### **Method 3: Docker Hub (Public)**
```bash
# Tag for Docker Hub
docker tag grievance-management-system username/grievance-system:latest

# Push to Docker Hub
docker push username/grievance-system:latest

# Others can pull and run
docker pull username/grievance-system:latest
docker run -d -p 8000:8000 -p 8501:8501 username/grievance-system:latest
```

---

## 🧪 Testing the Docker Setup

### **Health Check:**
```bash
# Check if API is responding
curl http://localhost:8000/

# Check if frontend is accessible
curl http://localhost:8501/
```

### **Functional Test:**
1. Open http://localhost:8501
2. Type: "**********" (sample mobile number)
3. Verify John Doe's complaint appears
4. Access admin panel: http://localhost:8501/admin
5. Login with admin/admin123
6. Verify 5 sample complaints are visible

---

## 🔧 Troubleshooting

### **Common Issues:**

#### **Port Already in Use:**
```bash
# Find process using the port
lsof -i :8501
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or use different ports
docker run -d --name grievance-management-system \
  -p 8002:8000 -p 8502:8501 \
  grievance-management-system
```

#### **Container Won't Start:**
```bash
# Check container logs
docker logs grievance-management-system

# Check if image built correctly
docker images | grep grievance

# Rebuild without cache
docker build -t grievance-management-system . --no-cache
```

#### **Database Issues:**
```bash
# Reset database (removes all data)
docker exec grievance-management-system rm -f /app/data/grievance_system.db
docker restart grievance-management-system
```

#### **Permission Issues (Linux/Mac):**
```bash
# Fix data directory permissions
sudo chown -R $USER:$USER data/
chmod 755 data/
```

---

## 🎯 Production Deployment

### **Docker Compose for Production:**
```yaml
version: '3.8'
services:
  grievance-system:
    image: grievance-management-system
    ports:
      - "80:8501"    # Frontend on port 80
      - "8000:8000"  # API on port 8000
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

### **With Nginx Reverse Proxy:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 🎉 Success Verification

### **✅ System is working correctly when:**
- Container status shows "Up" in `docker ps`
- http://localhost:8501 loads the chat interface
- http://localhost:8000/docs shows API documentation
- Admin panel accessible with admin/admin123
- Sample complaints visible in admin panel
- Can register new complaints through the interface

---

**🚀 Your Grievance Management System is now containerized and ready for easy sharing and deployment!**
