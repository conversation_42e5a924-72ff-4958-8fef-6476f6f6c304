# 🏗️ System Architecture Documentation

## 📁 Project Structure

```
cyfuture-assign1/
├── 📁 src/                          # Source code
│   ├── 📁 api/                      # Backend API services
│   │   ├── __init__.py
│   │   └── api_server.py            # FastAPI server with all endpoints
│   │
│   ├── 📁 frontend/                 # Frontend user interface
│   │   ├── __init__.py
│   │   ├── app.py                   # Main Streamlit application
│   │   └── 📁 pages/                # Streamlit pages
│   │       └── admin.py             # Admin dashboard
│   │
│   ├── 📁 core/                     # Core business logic
│   │   ├── __init__.py
│   │   ├── llm_handler.py           # LLM integration (Groq)
│   │   └── rag_system.py            # RAG and vector operations
│   │
│   ├── 📁 models/                   # Data models and schemas
│   │   ├── __init__.py
│   │   └── models.py                # Pydantic models and enums
│   │
│   ├── 📁 database/                 # Database management
│   │   ├── __init__.py
│   │   └── database.py              # SQLite operations and queries
│   │
│   ├── 📁 config/                   # Configuration management
│   │   ├── __init__.py
│   │   └── config.py                # Application configuration
│   │
│   ├── 📁 utils/                    # Utility functions
│   │   └── __init__.py
│   │
│   ├── 📁 tests/                    # Test files
│   │   ├── test_system.py           # System integration tests
│   │   ├── enhanced_features_demo.py # Feature demonstrations
│   │   └── exact_scenario_demo.py   # Scenario testing
│   │
│   └── 📁 docs/                     # Documentation assets
│       ├── architecture_diagram.png
│       └── system_flow.pdf
│
├── 📁 deployment/                   # Deployment configurations
│   ├── 📁 docker/                  # Docker configurations
│   │   ├── Dockerfile
│   │   ├── docker-compose.yml
│   │   └── nginx.conf
│   │
│   └── 📁 nginx/                    # Nginx configurations
│
├── 📁 scripts/                      # Setup and utility scripts
│   ├── setup.py                    # Original setup script
│   ├── enhanced_setup.py           # Professional setup script
│   ├── init_db.py                  # Database initialization
│   └── start_app.py                # Application starter
│
├── 📁 data/                         # Data files
│   └── grievance_system.db         # SQLite database
│
├── 📁 logs/                         # Application logs
│   └── app.log
│
├── 📁 docs/                         # Project documentation
│   ├── API_REFERENCE.md
│   ├── USER_GUIDE.md
│   └── DEPLOYMENT_GUIDE.md
│
├── 📄 README.md                     # Main project documentation
├── 📄 ARCHITECTURE.md               # This file
├── 📄 ENHANCEMENT_SUMMARY.md        # Enhancement details
├── 📄 requirements.txt              # Python dependencies
├── 📄 .env                          # Environment configuration
├── 📄 .gitignore                    # Git ignore rules
├── 📄 quick_start.py                # Quick start script
└── 📄 logging.conf                  # Logging configuration
```

## 🏛️ Architecture Layers

### 1. **Presentation Layer** 🎨
- **Streamlit Frontend** (`src/frontend/`)
  - Main chat interface (`app.py`)
  - Admin dashboard (`pages/admin.py`)
  - Responsive design with professional UI
  - Real-time updates and interactive components

### 2. **API Gateway Layer** 🔌
- **FastAPI Server** (`src/api/api_server.py`)
  - RESTful API endpoints
  - Automatic OpenAPI documentation
  - CORS middleware for cross-origin requests
  - Request validation and error handling

### 3. **Business Logic Layer** 🧠
- **Core Services** (`src/core/`)
  - **LLM Handler**: Groq integration for AI responses
  - **RAG System**: Vector embeddings and semantic search
  - **Conversation Memory**: Context awareness and user tracking
  - **Intent Recognition**: Advanced pattern matching

### 4. **Data Access Layer** 💾
- **Database Manager** (`src/database/database.py`)
  - SQLite operations with connection pooling
  - Optimized queries with proper indexing
  - Transaction management
  - Enhanced mobile number search

### 5. **Model Layer** 📊
- **Data Models** (`src/models/models.py`)
  - Pydantic models for validation
  - Enums for status and categories
  - Type hints for better code quality

## 🔄 Data Flow Architecture

```mermaid
graph TB
    A[User Input] --> B[Streamlit Frontend]
    B --> C[Intent Recognition]
    C --> D[Conversation Memory]
    D --> E[FastAPI Backend]
    E --> F[Business Logic]
    F --> G[Database Operations]
    F --> H[LLM Processing]
    H --> I[RAG System]
    I --> J[Vector Search]
    G --> K[SQLite Database]
    J --> L[Response Generation]
    L --> M[Context-Aware Reply]
    M --> B
```

## 🔧 Component Interactions

### **Frontend Components**
- **Main App**: Chat interface with conversation memory
- **Admin Panel**: Secure management dashboard
- **Authentication**: Session-based admin login

### **Backend Components**
- **API Endpoints**: RESTful services for all operations
- **Database Layer**: Optimized SQLite with enhanced search
- **AI Integration**: Groq LLM with fallback mechanisms

### **AI/ML Components**
- **Conversation Memory**: User context tracking
- **Intent Recognition**: Advanced pattern matching
- **RAG System**: Vector-based knowledge retrieval
- **Response Generation**: Context-aware replies

## 🔐 Security Architecture

### **Authentication & Authorization**
- Admin panel with secure login
- Session management with timeout
- Role-based access control

### **Data Protection**
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Secure database connections

### **API Security**
- CORS configuration
- Rate limiting (configurable)
- Request validation
- Error handling without information leakage

## 📊 Database Schema

### **Complaints Table**
```sql
CREATE TABLE complaints (
    complaint_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    mobile TEXT NOT NULL,
    complaint_details TEXT NOT NULL,
    category TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'Registered',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    priority TEXT DEFAULT 'Normal',
    assigned_to TEXT,
    resolution_notes TEXT
);
```

### **Status History Table**
```sql
CREATE TABLE status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    complaint_id TEXT NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_by TEXT,
    change_reason TEXT,
    changed_at TEXT NOT NULL,
    FOREIGN KEY (complaint_id) REFERENCES complaints (complaint_id)
);
```

### **Performance Indexes**
- `idx_mobile`: Fast mobile number lookups
- `idx_status`: Status-based filtering
- `idx_created_at`: Time-based queries
- `idx_category`: Category filtering
- `idx_priority`: Priority-based sorting

## 🚀 Deployment Architecture

### **Development Environment**
- Local SQLite database
- Streamlit dev server
- FastAPI with auto-reload

### **Production Environment**
- Docker containerization
- Nginx reverse proxy
- Load balancing support
- Centralized logging

### **Scalability Considerations**
- Horizontal scaling ready
- Database connection pooling
- Caching mechanisms
- Microservices architecture

## 🔄 CI/CD Pipeline

### **Development Workflow**
1. Code changes in feature branches
2. Automated testing
3. Code review process
4. Merge to main branch

### **Deployment Process**
1. Docker image building
2. Container orchestration
3. Health checks
4. Rolling updates

## 📈 Performance Optimizations

### **Database Optimizations**
- Proper indexing strategy
- Query optimization
- Connection pooling
- Prepared statements

### **Frontend Optimizations**
- Component caching
- Lazy loading
- Efficient state management
- Responsive design

### **Backend Optimizations**
- Async operations
- Request batching
- Response compression
- Caching strategies

## 🔍 Monitoring & Observability

### **Logging Strategy**
- Structured logging
- Log levels (DEBUG, INFO, WARN, ERROR)
- Centralized log aggregation
- Performance metrics

### **Health Monitoring**
- API health checks
- Database connectivity
- System resource monitoring
- Error rate tracking

## 🎯 Quality Assurance

### **Code Quality**
- Type hints throughout
- Comprehensive documentation
- Clean code principles
- SOLID design patterns

### **Testing Strategy**
- Unit tests for core functions
- Integration tests for APIs
- End-to-end testing
- Performance testing

### **Security Testing**
- Vulnerability scanning
- Penetration testing
- Security code review
- Dependency auditing

---

**🏗️ This architecture ensures scalability, maintainability, and professional-grade quality for the Grievance Management System.**
