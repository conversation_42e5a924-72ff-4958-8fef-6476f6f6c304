#!/usr/bin/env python3
"""
Comprehensive System Functionality Test
Tests all components of the Grievance Management System
"""

import requests
import json
import time
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database import DatabaseManager
from core.llm_handler import LLMHandler
from core.rag_system import RAGSystem
from models.models import ComplaintRequest, ComplaintCategory

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
STREAMLIT_URL = "http://localhost:8502"

def test_api_server():
    """Test API server connectivity"""
    print("🔄 Testing API Server...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ API Server is running")
            return True
        else:
            print(f"❌ API Server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Server connection failed: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("🔄 Testing Database...")
    try:
        db = DatabaseManager()
        db.init_database()
        print("✅ Database initialized successfully")
        
        # Test complaint registration
        complaint = ComplaintRequest(
            name="Test User",
            mobile="1234567890",
            complaint_details="Test complaint for system verification",
            category=ComplaintCategory.HARDWARE
        )
        
        result = db.register_complaint(complaint)
        if result:
            print(f"✅ Complaint registered: {result.complaint_id}")
            
            # Test retrieval
            retrieved = db.get_complaint_by_id(result.complaint_id)
            if retrieved:
                print("✅ Complaint retrieval successful")
            else:
                print("❌ Complaint retrieval failed")
                
            # Test mobile search
            mobile_complaints = db.get_complaints_by_mobile("1234567890")
            if mobile_complaints:
                print("✅ Mobile search successful")
            else:
                print("❌ Mobile search failed")
                
            return True
        else:
            print("❌ Complaint registration failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_llm_handler():
    """Test LLM handler functionality"""
    print("🔄 Testing LLM Handler...")
    try:
        llm = LLMHandler()
        
        # Test categorization
        category = llm.categorize_complaint("My laptop is not working")
        print(f"✅ LLM categorization: {category}")
        
        # Test response generation
        from models.models import UserContext
        context = UserContext(
            user_id="test_user",
            mobile="1234567890",
            conversation_history=[],
            current_intent="register_complaint"
        )
        
        response = llm.generate_response("I need help with my computer", context)
        if response:
            print("✅ LLM response generation successful")
            return True
        else:
            print("❌ LLM response generation failed")
            return False
            
    except Exception as e:
        print(f"❌ LLM Handler test failed: {e}")
        return False

def test_rag_system():
    """Test RAG system functionality"""
    print("🔄 Testing RAG System...")
    try:
        rag = RAGSystem()
        
        # Test contextual response
        context = rag.get_contextual_response("My laptop keeps crashing")
        if context and 'response' in context:
            print("✅ RAG contextual response successful")
            print(f"   Category: {context.get('category')}")
            print(f"   Response: {context.get('response')[:50]}...")
            return True
        else:
            print("❌ RAG contextual response failed")
            return False
            
    except Exception as e:
        print(f"❌ RAG System test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints"""
    print("🔄 Testing API Endpoints...")
    try:
        # Test complaint registration
        complaint_data = {
            "name": "API Test User",
            "mobile": "9999999999",
            "complaint_details": "API test complaint - network issues",
            "category": "Network"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/register-complaint",
            json=complaint_data,
            timeout=10
        )
        
        if response.status_code == 200:
            complaint_response = response.json()
            complaint_id = complaint_response.get('complaint_id')
            print(f"✅ API complaint registration: {complaint_id}")
            
            # Test status retrieval
            status_response = requests.get(
                f"{API_BASE_URL}/complaint-status/{complaint_id}",
                timeout=10
            )
            
            if status_response.status_code == 200:
                print("✅ API status retrieval successful")
            else:
                print("❌ API status retrieval failed")
                
            # Test mobile complaints
            mobile_response = requests.get(
                f"{API_BASE_URL}/user-complaints/9999999999",
                timeout=10
            )
            
            if mobile_response.status_code == 200:
                print("✅ API mobile complaints retrieval successful")
                return True
            else:
                print("❌ API mobile complaints retrieval failed")
                return False
        else:
            print(f"❌ API complaint registration failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API endpoints test failed: {e}")
        return False

def test_streamlit_app():
    """Test Streamlit app connectivity"""
    print("🔄 Testing Streamlit App...")
    try:
        response = requests.get(STREAMLIT_URL, timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit app is accessible")
            return True
        else:
            print(f"❌ Streamlit app returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Streamlit app connection failed: {e}")
        return False

def main():
    """Run comprehensive system tests"""
    print("=" * 80)
    print("🧪 GRIEVANCE MANAGEMENT SYSTEM - FUNCTIONALITY TEST")
    print("=" * 80)
    
    tests = [
        ("API Server", test_api_server),
        ("Database", test_database),
        ("LLM Handler", test_llm_handler),
        ("RAG System", test_rag_system),
        ("API Endpoints", test_api_endpoints),
        ("Streamlit App", test_streamlit_app)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name} Test:")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! System is fully functional.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
    
    print("\n🔗 Access URLs:")
    print(f"   Frontend: {STREAMLIT_URL}")
    print(f"   API Docs: {API_BASE_URL}/docs")
    print(f"   Admin Panel: {STREAMLIT_URL}/admin")

if __name__ == "__main__":
    main()
